{%- if section.settings.show_fixed_add_to_cart and product.available and buy_buttons_block != blank -%}
  <product-rerender id="{{ product_form_id }}-sticky-bar" observe-form="{{ product_form_id }}">
    {%- if product.selected_or_first_available_variant.available -%}
      <product-quick-add form="{{ product_form_id }}" class="product-quick-add" observe-form>
        <script>
          class ProductQuickAdd extends HTMLElement {
            constructor() {
              super();
              this.formId = this.getAttribute('form');
              this.form = document.getElementById(this.formId);
              this.mainForm = document.querySelector(`form[id="${this.formId}"]`);
              this.setupIntersectionObserver();
              this.setupScrollHandler();
            }

            setupIntersectionObserver() {
              // Watch main form to determine when to show the sticky add-to-cart
              const formObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                  if (!entry.isIntersecting && window.scrollY > entry.target.offsetTop) {
                    // Only add is-visible if we're not near the footer
                    if (!this.isNearFooter()) {
                      this.classList.add('is-visible');
                    }
                  } else {
                    this.classList.remove('is-visible');
                  }
                });
              }, { threshold: 0 });

              if (this.mainForm) {
                formObserver.observe(this.mainForm);
              }

              // Add scroll event listener to check footer proximity on scroll
              window.addEventListener('scroll', () => {
                if (this.isNearFooter()) {
                  // Force remove the visibility when near footer
                  this.classList.remove('is-visible');
                  this.style.display = 'none';
                } else {
                  // Reset display property when not near footer
                  this.style.display = '';

                  // Only add visible class if main form is not in viewport
                  if (this.mainForm && !this.isElementVisible(this.mainForm) && window.scrollY > this.mainForm.offsetTop) {
                    this.classList.add('is-visible');
                  }
                }
              });
            }

            // Check if we're close to or at the footer
            isNearFooter() {
              // Try multiple selectors to ensure we find the footer
              const footer = document.querySelector('footer') ||
                             document.querySelector('.footer') ||
                             document.querySelector('.shopify-section--footer');

              if (!footer) return false;

              const footerTop = footer.getBoundingClientRect().top;
              const windowHeight = window.innerHeight || document.documentElement.clientHeight;

              // Return true if footer is visible or about to be visible
              return footerTop < windowHeight + 100; // Add 100px buffer before footer
            }

            // Check if element is visible in viewport
            isElementVisible(element) {
              const rect = element.getBoundingClientRect();
              return (
                rect.top < (window.innerHeight || document.documentElement.clientHeight) &&
                rect.bottom >= 0
              );
            }

            setupScrollHandler() {
              // This function is intentionally left empty
              // It's here for compatibility with the original component
            }
          }

          if (!customElements.get('product-quick-add')) {
            customElements.define('product-quick-add', ProductQuickAdd);
          }
        </script>

        {%- assign button_label = 'Secure the Bag' -%}
        {%- assign image = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}

        <div class="product-quick-add__container">
          <div class="product-quick-add__left">
            {%- if image != blank -%}
              <div class="product-quick-add__image-container">
                <img src="{{ image | image_url: width: 400 }}" alt="{{ product.title }}" class="product-quick-add__image">
              </div>
            {%- endif -%}
            <div>
              <p class="product-quick-add__title">{{ product.title }}</p>
              <div class="product-quick-add__price-container">
                <p class="product-quick-add__price">
                  {%- comment -%}Custom pricing: Always show $55 as sale price and $69 as crossed-out regular price{%- endcomment -%}
                  <span class="sale-price">$55</span>
                  <span class="compare-price">$69</span>
                  <span class="save-badge one-time-badge">SAVE $14</span>
                  <span class="save-badge subscription-badge" style="display: none;">SAVE 30%</span>
                </p>
              </div>
            </div>
          </div>

          <div class="product-quick-add__center">
            <!-- Desktop purchase options -->
            <div class="product-quick-add__purchase-options desktop-purchase-options">
              <button type="button" class="product-quick-add__option selected" data-option="one-time">ONE-TIME PURCHASE</button>
              <button type="button" class="product-quick-add__option" data-option="subscription">MONTHLY SUBSCRIPTION</button>
            </div>

            <!-- Mobile purchase options with price -->
            <div class="mobile-purchase-container">
              <div class="mobile-price-display">
                <div class="mobile-price-wrapper">
                  <span class="mobile-current-price">$55</span>
                  <span class="mobile-compare-price">$69</span>
                </div>
                <span class="mobile-save-badge one-time-badge">SAVE $14</span>
                <span class="mobile-save-badge subscription-badge" style="display: none;">SAVE 30%</span>
              </div>
            </div>

            <!-- Mobile buttons in separate container -->
            <div class="mobile-buttons-container">
              <button type="button" class="product-quick-add__option selected" data-option="one-time">BUY ONCE</button>
              <button type="button" class="product-quick-add__option" data-option="subscription">MONTHLY</button>
            </div>
          </div>

          <div class="product-quick-add__right">
            <button type="submit" class="product-quick-add__button" form="{{ product_form_id }}">
              <span class="button-text">{{ button_label }}</span>
              <span class="button__loader">
                <span></span>
                <span></span>
                <span></span>
              </span>
            </button>
          </div>
        </div>

        <script>
          document.addEventListener('DOMContentLoaded', function() {
            const options = document.querySelectorAll('.product-quick-add__option');
            // Get desktop price elements
            const salePriceElement = document.querySelector('.product-quick-add__price .sale-price');
            const regularPriceElement = document.querySelector('.product-quick-add__price span:not(.compare-price)');
            const priceElement = salePriceElement || regularPriceElement;

            // Get mobile price elements
            const mobilePriceElement = document.querySelector('.mobile-current-price');
            const mobileComparePriceElement = document.querySelector('.mobile-compare-price');

            const regularPrice = '55'; // Custom sale price for one-time purchase
            const compareAtPrice = '69'; // Custom regular price (crossed out)
            const subscriptionPrice = '42'; // Updated subscription price

            // Function to update the displayed price
            function updatePrice(isSubscription) {
              // Update desktop price
              if (priceElement) {
                if (isSubscription) {
                  priceElement.textContent = '$' + subscriptionPrice;

                  // Handle compare price for subscription
                  const comparePrice = document.querySelector('.product-quick-add__price .compare-price');
                  if (comparePrice) {
                    comparePrice.textContent = '$60'; // Updated compare price for subscription
                  }
                } else {
                  priceElement.textContent = '$' + regularPrice;

                  // Reset compare price for one-time purchase
                  const comparePrice = document.querySelector('.product-quick-add__price .compare-price');
                  if (comparePrice) {
                    comparePrice.textContent = '$' + compareAtPrice;
                  }
                }
              }

              // Update mobile price
              if (mobilePriceElement) {
                if (isSubscription) {
                  mobilePriceElement.textContent = '$' + subscriptionPrice;
                  if (mobileComparePriceElement) {
                    mobileComparePriceElement.textContent = '$60'; // Updated compare price for subscription
                  }
                } else {
                  mobilePriceElement.textContent = '$' + regularPrice;
                  if (mobileComparePriceElement) {
                    mobileComparePriceElement.textContent = '$' + compareAtPrice;
                  }
                }
              }
            }

            // Initialize prices and save badges on page load
            const initialSelectedOption = document.querySelector('.product-quick-add__option.selected');
            if (initialSelectedOption) {
              const initialOptionType = initialSelectedOption.getAttribute('data-option');
              const initialIsSubscription = initialOptionType === 'subscription';

              // Initialize prices
              updatePrice(initialIsSubscription);

              // Initialize save badges (both desktop and mobile)
              const oneTimeBadges = document.querySelectorAll('.one-time-badge');
              const subscriptionBadges = document.querySelectorAll('.subscription-badge');

              oneTimeBadges.forEach(badge => {
                badge.style.display = initialIsSubscription ? 'none' : 'inline-block';
              });

              subscriptionBadges.forEach(badge => {
                badge.style.display = initialIsSubscription ? 'inline-block' : 'none';
              });
            }

            options.forEach(option => {
              option.addEventListener('click', function() {
                // Remove selected class from all options
                options.forEach(opt => opt.classList.remove('selected'));

                // Add selected class to clicked option
                this.classList.add('selected');

                // Get the selected option type
                const optionType = this.getAttribute('data-option');
                const isSubscription = optionType === 'subscription';

                // Update the price display
                updatePrice(isSubscription);

                // Toggle save badges (both desktop and mobile)
                const oneTimeBadges = document.querySelectorAll('.one-time-badge');
                const subscriptionBadges = document.querySelectorAll('.subscription-badge');

                oneTimeBadges.forEach(badge => {
                  badge.style.display = isSubscription ? 'none' : 'inline-block';
                });

                subscriptionBadges.forEach(badge => {
                  badge.style.display = isSubscription ? 'inline-block' : 'none';
                });

                // Find the subscription selector in the main form and update it
                const subscriptionRadios = document.querySelectorAll('input[name="selling_plan"]');
                if (subscriptionRadios.length > 0) {
                  if (isSubscription) {
                    // Select the first subscription option
                    const subscriptionOption = Array.from(subscriptionRadios).find(radio => radio.value !== '');
                    if (subscriptionOption) subscriptionOption.checked = true;
                  } else {
                    // Select the one-time purchase option
                    const oneTimeOption = Array.from(subscriptionRadios).find(radio => radio.value === '');
                    if (oneTimeOption) oneTimeOption.checked = true;
                  }
                }
              });
            });

            // Add form submission event listener for loading animation
            document.getElementById('{{ product_form_id }}').addEventListener('submit', function(event) {
              // Show loading animation
              const submitButton = document.querySelector('.product-quick-add__button');
              const buttonText = submitButton.childNodes[0].nodeValue.trim();
              const loaderElement = submitButton.querySelector('.button__loader');

              // Set fixed dimensions to prevent size changes
              const originalHeight = submitButton.offsetHeight;
              const originalWidth = submitButton.offsetWidth;
              submitButton.style.height = originalHeight + 'px';
              submitButton.style.width = originalWidth + 'px';
              submitButton.style.minWidth = originalWidth + 'px';
              submitButton.style.position = 'relative';

              // Hide the button text and show the loader
              const buttonTextElement = submitButton.querySelector('.button-text');
              buttonTextElement.style.display = 'none';
              loaderElement.style.opacity = '1';

              // Animate the dots with a simpler approach that matches the original button
              const dots = loaderElement.querySelectorAll('span');
              dots.forEach((dot, index) => {
                // Set initial state
                dot.style.opacity = '1';

                // Create keyframe animation that matches the original button
                const animation = dot.animate([
                  { opacity: 1 },
                  { opacity: 0.2 }
                ], {
                  duration: 600, // Slower animation to match original
                  iterations: Infinity,
                  direction: 'alternate',
                  delay: index * 200, // More noticeable delay between dots
                  easing: 'ease-in-out' // Smoother animation
                });
              });

              // Close the sticky add to cart after submission with a slight delay
              setTimeout(() => {
                document.querySelector('.product-quick-add').classList.remove('is-visible');
              }, 500);
            });
          });
        </script>
      </product-quick-add>
    {%- endif -%}
  </product-rerender>
{%- endif -%}
