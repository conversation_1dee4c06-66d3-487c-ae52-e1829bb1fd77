<style>
  /* New styling for product gallery navigation arrows */
  .product-gallery__media-list-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
  }

  /* Fix for Safari horizontal scrollbar */
  .product-gallery__media-list {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
  }

  /* Enable sticky positioning for product gallery on desktop without scrollbar */
  @media screen and (min-width: 1000px) {
    .product-media-section {
      position: sticky;
      top: calc(var(--sticky-area-height) + 20px);
      height: auto; /* Allow natural height */
      max-height: none; /* Remove max-height constraint */
      overflow: visible !important; /* Force visible overflow to prevent scrollbar */
      align-self: flex-start;
    }

    /* Ensure no scrollbar appears in any browser */
    .product-media-section::-webkit-scrollbar {
      display: none; /* Hide scrollbar in WebKit browsers */
    }

    .product-media-section {
      scrollbar-width: none; /* Hide scrollbar in Firefox */
      -ms-overflow-style: none; /* Hide scrollbar in IE/Edge */
    }
  }

  .product-gallery__nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent white */
    border: none;
    width: 40px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    touch-action: manipulation;
    cursor: pointer;
  }

  .product-gallery__nav-arrow:hover {
    background-color: #47DE47;
  }

  .product-gallery__nav-arrow--prev {
    left: 0;
    border-radius: 0 50% 50% 0;
    padding: 0;
    justify-content: flex-start;
  }

  .product-gallery__nav-arrow--next {
    right: 0;
    border-radius: 50% 0 0 50%;
    padding: 0;
    justify-content: flex-end;
  }

  .product-gallery__nav-arrow--prev .circle-button {
    margin-left: 3px;
  }

  .product-gallery__nav-arrow--next .circle-button {
    margin-right: 3px;
  }

  .product-gallery__nav-arrow .circle-button {
    background: transparent;
    box-shadow: none;
    height: 30px;
    width: 30px;
  }

  .product-gallery__nav-arrow:hover .circle-button {
    background: transparent;
  }

  /* Mobile specific adjustments for navigation arrows */
  @media screen and (max-width: 999px) {
    /* Make the product gallery full width on mobile */
    .product-gallery {
      width: 100vw !important;
      margin-left: calc(-50vw + 50%) !important;
      margin-right: calc(-50vw + 50%) !important;
      max-width: 100vw !important;
    }

    .product-gallery__media-list-wrapper {
      overflow: visible;
      position: relative;
      width: 100%;
      padding: 0;
    }

    .product-gallery__nav-arrow {
      width: 35px;
      height: 40px;
      /* Ensure arrows are always at the edges on mobile */
      position: absolute;
      z-index: 10;
    }

    .product-gallery__nav-arrow--prev {
      left: 0;
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
    }

    .product-gallery__nav-arrow--next {
      right: 0;
      border-top-left-radius: 20px;
      border-bottom-left-radius: 20px;
    }

    /* Make sure arrows are only visible on the product images */
    .product-gallery__media-list-wrapper .product-gallery__nav-arrow {
      opacity: 0.9;
      min-width: 44px; /* Larger touch target */
      min-height: 44px; /* Larger touch target */
    }

    /* Make media carousel full width to extend to edges of screen */
    .product-gallery__media-list.bleed,
    .product-gallery__media-list.full-bleed {
      position: relative;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      left: 0 !important;
      right: 0 !important;
    }

    /* Remove any padding or margins from carousel items */
    .product-gallery__media {
      position: relative;
      width: 100%;
      padding: 0 !important;
      margin: 0 !important;
      scroll-snap-align: center;
      flex-shrink: 0;
    }

    /* Override any existing padding classes on mobile */
    .product-gallery .scroll-area,
    .product-gallery .bleed,
    .product-gallery .full-bleed {
      padding-left: 0 !important;
      padding-right: 0 !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
      width: 100% !important;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Function to initialize all product galleries on the page
    function initProductGalleries() {
      const galleries = document.querySelectorAll('product-gallery');
      const isMobile = window.innerWidth < 1000;

      galleries.forEach(gallery => {
        const carouselId = gallery.querySelector('media-carousel')?.id;
        if (!carouselId) return;

        const prevButton = gallery.querySelector('.js-prev-button');
        const nextButton = gallery.querySelector('.js-next-button');
        const carousel = document.getElementById(carouselId);

        if (!prevButton || !nextButton || !carousel) return;

        // Enhance touch targets for mobile
        if (isMobile) {
          // Make sure arrows are easier to tap on mobile
          prevButton.style.touchAction = 'manipulation';
          nextButton.style.touchAction = 'manipulation';

          // Ensure arrows stay in view on mobile
          const mediaWrapper = gallery.querySelector('.product-gallery__media-list-wrapper');
          if (mediaWrapper) {
            mediaWrapper.style.position = 'relative';
            mediaWrapper.style.overflow = 'visible';
          }

          // Fix for carousel on mobile
          const mediaList = gallery.querySelector('.product-gallery__media-list');
          if (mediaList) {
            mediaList.style.scrollSnapType = 'x mandatory';
            mediaList.style.webkitOverflowScrolling = 'touch';

            // Ensure all media items have proper snap alignment
            const mediaItems = mediaList.querySelectorAll('.product-gallery__media');
            mediaItems.forEach(item => {
              item.style.scrollSnapAlign = 'center';
            });
          }
        }

        // Update button visibility on initialization
        updateButtonsVisibility();

        // Update buttons visibility when carousel changes
        carousel.addEventListener('carousel:change', updateButtonsVisibility);
        carousel.addEventListener('carousel:settle', updateButtonsVisibility);

        // Also update on scroll for mobile
        if (isMobile) {
          carousel.addEventListener('scroll', function() {
            // Use requestAnimationFrame to avoid performance issues
            requestAnimationFrame(updateButtonsVisibility);
          }, { passive: true });

          // Update on touchend to ensure correct state after swipe
          carousel.addEventListener('touchend', function() {
            // Delay slightly to allow scroll to settle
            setTimeout(updateButtonsVisibility, 100);
          }, { passive: true });
        }

        // Ensure navigation buttons work properly on mobile
        if (isMobile) {
          // Remove existing event listeners and add new ones
          const newPrevButton = prevButton.cloneNode(true);
          const newNextButton = nextButton.cloneNode(true);

          prevButton.parentNode.replaceChild(newPrevButton, prevButton);
          nextButton.parentNode.replaceChild(newNextButton, nextButton);

          newPrevButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            carousel.previous();
          });

          newNextButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            carousel.next();
          });
        }

        function updateButtonsVisibility() {
          // Get all visible slides
          const visibleSlides = Array.from(carousel.querySelectorAll('.product-gallery__media')).filter(item => !item.hidden);

          // Find the current slide - either the one with is-selected class or determine by scroll position
          let currentIndex = visibleSlides.findIndex(slide => slide.classList.contains('is-selected'));

          // If we couldn't find a selected slide by class, determine by scroll position
          if (currentIndex === -1 && isMobile) {
            const scrollLeft = carousel.scrollLeft;
            const carouselWidth = carousel.offsetWidth;

            // Calculate which slide is most visible based on scroll position
            currentIndex = Math.round(scrollLeft / carouselWidth);

            // Ensure index is within bounds
            currentIndex = Math.max(0, Math.min(currentIndex, visibleSlides.length - 1));
          }

          // Default to first slide if we still can't determine
          if (currentIndex === -1) currentIndex = 0;

          // Update button visibility based on current position
          if (isMobile) {
            // For mobile, get the actual buttons that are now in the DOM after replacement
            const currentPrevButton = gallery.querySelector('.js-prev-button');
            const currentNextButton = gallery.querySelector('.js-next-button');

            if (currentPrevButton && currentNextButton) {
              currentPrevButton.style.display = currentIndex <= 0 ? 'none' : '';
              currentNextButton.style.display = currentIndex >= visibleSlides.length - 1 ? 'none' : '';
            }
          } else {
            // For desktop
            prevButton.style.display = currentIndex <= 0 ? 'none' : '';
            nextButton.style.display = currentIndex >= visibleSlides.length - 1 ? 'none' : '';
          }
        }
      });
    }

    // Initialize on page load
    initProductGalleries();

    // Re-initialize when the Shopify section is reloaded in the editor
    document.addEventListener('shopify:section:load', initProductGalleries);

    // Update when window is resized (responsive behavior)
    let resizeTimer;
    window.addEventListener('resize', function() {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(function() {
        initProductGalleries();
      }, 250);
    });

    // Fix for touch events on mobile
    document.addEventListener('touchstart', function(e) {
      const carousel = e.target.closest('media-carousel');
      if (carousel && window.innerWidth < 1000) {
        // Ensure we don't have conflicting touch handlers
        carousel.style.touchAction = 'pan-y';
      }
    }, { passive: true });

    // Fix for potential conflicts with gesture events
    document.addEventListener('gesturestart', function(e) {
      if (e.target.closest('media-carousel')) {
        e.preventDefault();
      }
    }, { passive: false });
  });
</script>

{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
PRODUCT GALLERY COMPONENT
----------------------------------------------------------------------------------------------------------------------

This component is used to show the product gallery (used in product page and featured product section)

********************************************
Supported variables
********************************************

* product: the product to render (if empty, renders placeholder content)
* product_form_id: the product form id
{%- endcomment -%}

{%- assign default_media = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}
{%- capture product_gallery_id -%}product-gallery-{{ product.id }}-{{ section.id }}{%- endcapture -%}

{%- comment -%}
IMPLEMENTATION NOTE: Shopify does not natively offer a way to create a set of images per variant. This is often
pretty limited when you have a lot of colors and would like to only see the images specific to a given color. To
goes around that, Impact offers a hack using alt tags whose usage is described here: https://support.maestrooo.com/article/187-product-creating-variant-image-set
The implementation is rather complex and inefficient due to a lot of string parsings, but it is, unfortunately, the
only way to do it on Shopify right now.
{%- endcomment -%}

{%- liquid
  assign filtered_indexes = null | sort

  if product.variants.size > 1
    for media in product.media
      if media.alt contains '#' and media.alt != product.title
        assign alt_parts = media.alt | split: '#'

        assign media_group_parts = alt_parts.last | split: '_'
        assign option = product.options_by_name[media_group_parts.first]
        assign option_value = option.selected_value | downcase

        assign downcase_group_value = media_group_parts.last | strip | downcase

        if option_value != downcase_group_value and media != default_media
          assign media_position = media.position | sort
          assign filtered_indexes = filtered_indexes | concat: media_position
        endif
      endif
    endfor
  endif
-%}

<product-gallery form="{{ product_form_id }}" filtered-indexes="{{ filtered_indexes | json }}" {%- if section.settings.enable_image_zoom -%}allow-zoom="{{ section.settings.max_image_zoom_level }}"{% endif %} class="product-gallery {% if section.settings.mobile_carousel_control contains 'dots' %}product-gallery--mobile-dots{% endif %} {% if section.settings.desktop_media_layout contains 'grid' %}product-gallery--desktop-grid{% else %}product-gallery--desktop-carousel{% endif %} {% if section.settings.desktop_media_layout == 'carousel_thumbnails_left' %}product-gallery--desktop-thumbnails-left{% endif %} {% if section.settings.mobile_media_size == 'expanded' %}product-gallery--mobile-expanded{% endif %}">
  {%- capture page_dots -%}
    <page-dots class="page-dots {% if section.settings.mobile_carousel_control == 'floating_dots' %}page-dots--blurred{% endif %} md:hidden" aria-controls="{{ product_gallery_id }}">
      {%- for media in product.media -%}
        <button type="button" class="tap-area" {% if filtered_indexes contains media.position %}hidden{% endif %} aria-current="{% if media == default_media %}true{% else %}false{% endif %}">
          <span class="sr-only">{{ 'general.accessibility.go_to_item' | t: index: media.position }}</span>
        </button>
      {%- endfor -%}
    </page-dots>
  {%- endcapture -%}

  <div class="product-gallery__ar-wrapper">
    <div class="product-gallery__media-list-wrapper">
      {%- if section.settings.desktop_media_layout contains 'carousel' and product.media.size > 1 -%}
        <!-- Left navigation arrow (fixed) -->
        <button is="prev-button" aria-controls="{{ product_gallery_id }}" class="product-gallery__nav-arrow product-gallery__nav-arrow--prev js-prev-button" aria-label="{{ 'product.gallery.previous' | t }}">
          <div class="circle-button circle-button--fill circle-button--lg">
            {%- render 'icon' with 'chevron-left' -%}
          </div>
        </button>

        <!-- Right navigation arrow (fixed) -->
        <button is="next-button" aria-controls="{{ product_gallery_id }}" class="product-gallery__nav-arrow product-gallery__nav-arrow--next js-next-button" aria-label="{{ 'product.gallery.next' | t }}">
          <div class="circle-button circle-button--fill circle-button--lg">
            {%- render 'icon' with 'chevron-right' -%}
          </div>
        </button>

        <!-- Keep original cursor but hidden by default -->
        <custom-cursor class="product-gallery__cursor" hidden>
          <div class="circle-button circle-button--fill circle-button--lg">
            {%- render 'icon' with 'chevron-right' -%}
          </div>
        </custom-cursor>
      {%- endif -%}

      {%- comment -%}
      --------------------------------------------------------------------------------------------------------------------
      MEDIA LIST
      --------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      <media-carousel desktop-mode="{{ section.settings.desktop_media_layout }}" adaptive-height initial-index="{{ default_media.position | minus: 1 }}" {% if section.settings.enable_video_autoplay %}autoplay{% endif %} id="{{ product_gallery_id }}" class="product-gallery__media-list {% if section.settings.mobile_media_size == 'expanded' %}full-bleed{% else %}bleed{% endif %} scroll-area md:unbleed">
        {%- assign media_attached_to_variant = product.images | where: 'attached_to_variant?', true | map: 'src' -%}

        {%- for media in product.media -%}
          {%- case section.settings.desktop_media_layout -%}
            {%- when 'carousel_thumbnails_bottom' -%}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), min(760px, 42vw){%- endcapture -%}

            {%- when 'carousel_thumbnails_left' -%}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), min(730px, 40vw){%- endcapture -%}

            {%- when 'grid' -%}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), min(580px, 30vw){%- endcapture -%}

            {% when 'grid_highlight' %}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), {% if forloop.first or media_attached_to_variant contains media.src %}min(1200px, 60vw){% else %}min(580px, 30vw){% endif %}{%- endcapture -%}
          {%- endcase -%}

          <div class="product-gallery__media {% if media.alt contains '@expand' %}product-gallery__media--expand{% endif %} {% if section.settings.mobile_carousel_control != 'free_scroll' %}snap-center{% endif %}" data-media-type="{{ media.media_type }}" data-media-id="{{ media.id }}" {% if filtered_indexes contains media.position %}hidden{% endif %}>
            {%- if default_media == media -%}
              {%- assign preload = true -%}
            {%- else -%}
              {%- assign preload = false -%}
            {%- endif -%}

            {%- render 'media', media: media, sizes: sizes, preload: preload, autoplay: section.settings.enable_video_autoplay, loop: section.settings.enable_video_looping, group: 'product' -%}

            {%- if section.settings.enable_image_zoom and section.settings.desktop_media_layout contains 'grid' and media.media_type == 'image' -%}
              <div class="product-gallery__zoom hidden md:block">
                <button type="button" is="product-zoom-button" class="circle-button circle-button--fill ring">
                  <span class="sr-only">{{ 'product.gallery.zoom' | t }}</span>
                  {%- render 'icon' with 'image-zoom' -%}
                </button>
              </div>
            {%- endif -%}
          </div>
        {%- endfor -%}
      </media-carousel>

      {%- if section.settings.enable_image_zoom -%}
        <div class="product-gallery__zoom {% if section.settings.desktop_media_layout contains 'grid' %}md:hidden{% endif %}">
          <button type="button" is="product-zoom-button" class="circle-button circle-button--fill ring">
            <span class="sr-only">{{ 'product.gallery.zoom' | t }}</span>
            {%- render 'icon' with 'image-zoom' -%}
          </button>
        </div>
      {%- endif -%}

      {%- if product.media.size > 1 and section.settings.mobile_carousel_control == 'floating_dots' -%}
        {{- page_dots -}}
      {%- endif -%}
    </div>

    {%- comment -%}
    ----------------------------------------------------------------------------------------------------------------------
    VIEW IN YOUR SPACE
    ----------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}

    {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}

    {%- if first_3d_model -%}
      <link rel="stylesheet" href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css" media="print" onload="this.media='all'">

      <button class="button button--lg button--subdued w-full" data-shopify-xr data-shopify-model3d-id="{{ first_3d_model.id }}" data-shopify-model3d-default-id="{{ first_3d_model.id }}" data-shopify-title="{{ product.title | escape }}" data-shopify-xr-hidden>
        <span class="text-with-icon justify-center">
          {%- render 'icon' with 'media-view-in-space' -%}
          {{- 'product.general.view_in_space' | t -}}
        </span>
      </button>
    {%- endif -%}
  </div>

  {%- comment -%}
  --------------------------------------------------------------------------------------------------------------------
  CONTROLS
  --------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {%- if product.media.size > 1 -%}
    {%- if section.settings.mobile_carousel_control == 'dots' -%}
      {{- page_dots -}}
    {%- endif -%}

    {%- if section.settings.mobile_carousel_control == 'thumbnails' or section.settings.desktop_media_layout contains 'thumbnails' -%}
      <scroll-shadow class="product-gallery__thumbnail-list-wrapper">
        <page-dots align-selected class="product-gallery__thumbnail-list scroll-area bleed md:unbleed" aria-controls="{{ product_gallery_id }}">
          {%- for media in product.media -%}
            <button type="button" class="product-gallery__thumbnail" {% if filtered_indexes contains media.position %}hidden{% endif %} aria-current="{% if media == default_media %}true{% else %}false{% endif %}" aria-label="{{ 'general.accessibility.go_to_item' | t: index: forloop.index | escape }}">
              {{- media | image_url: width: media.preview_image.width | image_tag: loading: 'lazy', sizes: '(max-width: 699px) 56px, 64px', widths: '56,64,112,128,168,192', class: 'object-contain rounded-sm' -}}

              {%- unless media.media_type == 'image' -%}
                <div class="product-gallery__media-badge">
                  {%- if media.media_type == 'model' -%}
                    {%- render 'icon' with 'play-model', width: 10, height: 12 -%}
                  {%- else -%}
                    {%- render 'icon' with 'play-video', width: 7, height: 9 -%}
                  {%- endif -%}
                </div>
              {% endunless %}
            </button>
          {%- endfor -%}
        </page-dots>
      </scroll-shadow>
    {%- endif -%}
  {%- endif -%}
</product-gallery>
