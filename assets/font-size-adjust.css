/*
 * Enhanced font size adjustment CSS to prevent layout shifts
 * This file contains improved size-adjust values and font loading states
 */

/* Font loading states */
.fonts-loading body {
  /* Prevent FOUT (Flash of Unstyled Text) during font loading */
  transition: none !important;
  /* Force content to render immediately with fallback fonts */
  font-display: swap !important;
}

/* Apply subtle transition when critical fonts are loaded */
.critical-fonts-loaded body {
  transition: opacity 0.1s ease-in-out;
}

/* Apply subtle transition when all fonts are loaded */
.fonts-loaded body {
  transition: opacity 0.1s ease-in-out;
}

/* Hide text flashing by using opacity transitions */
.fonts-loading .heading-text,
.fonts-loading h1,
.fonts-loading h2,
.fonts-loading h3,
.fonts-loading h4,
.fonts-loading h5,
.fonts-loading h6,
.fonts-loading .button,
.fonts-loading .product-title,
.fonts-loading .price {
  transition: color 0.1s ease-in-out;
}

/* Ensure content is visible even if fonts fail to load */
.fonts-error body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
}

/* FormaDJRDisplay size adjustments to match system fonts */
@font-face {
  font-family: 'FormaDJRDisplay-fallback';
  size-adjust: 98.2%;
  ascent-override: 94%;
  descent-override: 22%;
  line-gap-override: 0%;
  src: local(-apple-system), local(BlinkMacSystemFont), local('Segoe UI'), local(Helvetica), local(Arial), local(sans-serif);
  font-display: swap;
}

/* Meltmino size adjustments to match system fonts */
@font-face {
  font-family: 'Meltmino-fallback';
  size-adjust: 97%;
  ascent-override: 93%;
  descent-override: 23%;
  line-gap-override: 0%;
  src: local(-apple-system), local(BlinkMacSystemFont), local('Segoe UI'), local(Helvetica), local(Arial), local(sans-serif);
  font-display: swap;
}

/* FormaDJRBanner size adjustments */
@font-face {
  font-family: 'FormaDJRBanner-fallback';
  size-adjust: 99%;
  ascent-override: 95%;
  descent-override: 21%;
  line-gap-override: 0%;
  src: local(-apple-system), local(BlinkMacSystemFont), local('Segoe UI'), local(Helvetica), local(Arial), local(sans-serif);
  font-display: swap;
}

/* FormaDJRText size adjustments */
@font-face {
  font-family: 'FormaDJRText-fallback';
  size-adjust: 97.5%;
  ascent-override: 93.5%;
  descent-override: 22.5%;
  line-gap-override: 0%;
  src: local(-apple-system), local(BlinkMacSystemFont), local('Segoe UI'), local(Helvetica), local(Arial), local(sans-serif);
  font-display: swap;
}

/* FormaDJRDeck size adjustments */
@font-face {
  font-family: 'FormaDJRDeck-fallback';
  size-adjust: 98%;
  ascent-override: 94%;
  descent-override: 22%;
  line-gap-override: 0%;
  src: local(-apple-system), local(BlinkMacSystemFont), local('Segoe UI'), local(Helvetica), local(Arial), local(sans-serif);
  font-display: swap;
}

/* FormaDJRMicro size adjustments */
@font-face {
  font-family: 'FormaDJRMicro-fallback';
  size-adjust: 96%;
  ascent-override: 92.5%;
  descent-override: 23.5%;
  line-gap-override: 0%;
  src: local(-apple-system), local(BlinkMacSystemFont), local('Segoe UI'), local(Helvetica), local(Arial), local(sans-serif);
  font-display: swap;
}

/* Add font family fallback rules */
.fonts-loading .heading-font,
.fonts-loading h1,
.fonts-loading h2,
.fonts-loading h3,
.fonts-loading h4,
.fonts-loading h5,
.fonts-loading h6 {
  font-family: 'FormaDJRDisplay-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
}

.fonts-loading .body-font,
.fonts-loading p,
.fonts-loading .text-content {
  font-family: 'FormaDJRText-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
}

.fonts-loading .meltmino-font,
.fonts-loading .special-heading {
  font-family: 'Meltmino-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
}
