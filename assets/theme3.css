.product-gallery__media-list {
    grid: var(--product-gallery-media-list-grid);
    gap: var(--product-gallery-media-list-gap);
    align-items: start;
    transition: height .1s;
    display: grid;
  }
  
  .product-gallery__media {
    position: relative;
  }
  
  .product-gallery__media img {
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  .product-gallery .page-dots--blurred {
    z-index: 1;
    justify-self: center;
    margin-inline-start: var(--spacing-2);
    margin-inline-end: var(--spacing-2);
    position: absolute;
    bottom: var(--spacing-2);
  }
  
  .product-gallery__thumbnail-list {
    align-items: end;
    gap: var(--spacing-2);
    grid-auto-columns: 56px;
    grid-auto-flow: column;
    display: grid;
  }
  
  .product-gallery__thumbnail {
    position: relative;
  }
  
  .product-gallery__thumbnail:after {
    content: "";
    opacity: 0;
    background: currentColor;
    width: 100%;
    height: 2px;
    margin-block-start: 4px;
    transition: opacity .15s;
    display: block;
  }
  
  .product-gallery__thumbnail[aria-current="true"]:after {
    opacity: 1;
  }
  
  .product-gallery__media-badge {
    width: var(--spacing-5);
    height: var(--spacing-5);
    background: rgb(var(--background-primary));
    border-radius: var(--rounded-button);
    border: 1px solid rgb(var(--text-color) / .12);
    place-content: center;
    display: grid;
    position: absolute;
    bottom: var(--spacing-2-5);
  }
  
  .product-gallery__media-badge:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--spacing-1);
  }
  
  .product-gallery__media-badge:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--spacing-1);
  }
  
  .product-gallery__zoom {
    transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    position: absolute;
    top: var(--spacing-4);
  }
  
  .product-gallery__zoom:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--spacing-4);
  }
  
  .product-gallery__zoom:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--spacing-4);
  }
  
  .product-gallery__zoom > .circle-button {
    width: 2.1875rem;
    height: 2.1875rem;
  }
  
  .product-gallery__cursor.is-half-start svg {
    transform: rotate(180deg);
  }
  
  [data-shopify-xr-hidden] {
    visibility: hidden;
  }
  
  @media screen and (min-width: 700px) {
    .product-gallery__thumbnail-list {
      grid-auto-columns: 64px;
    }
  }
  
  @media screen and (max-width: 999px) {
    .product-gallery__thumbnail-list-wrapper {
      --scroll-shadow-size: 0px;
    }
  
    .product-gallery--mobile-dots .product-gallery__thumbnail-list-wrapper {
      display: none;
    }
  
    .product-gallery:has([data-media-type*="video"].is-selected) .product-gallery__zoom {
      opacity: 0;
      visibility: hidden;
      transform: scale(.8);
    }
  
    .product-gallery--mobile-expanded .product-gallery__media, .product-gallery--mobile-expanded .product-gallery__media > * {
      border-radius: 0;
    }
  
    .product-gallery--mobile-expanded .product-gallery__zoom:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: 0;
    }
  
    .product-gallery--mobile-expanded .product-gallery__zoom:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: 0;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .product-gallery {
      gap: var(--spacing-6) var(--spacing-12);
    }
  
    .product-gallery__thumbnail-list-wrapper {
      align-items: center;
      gap: var(--spacing-4);
      grid-auto-flow: column;
      display: grid;
      position: relative;
    }
  
    .product-gallery__thumbnail-list {
      gap: var(--spacing-4);
    }
  
    .product-gallery__thumbnail-list-wrapper > button {
      z-index: 1;
      opacity: 0;
      margin-top: -3px;
      transition: opacity .1s, transform .1s;
      position: absolute;
      transform: scale(.6);
    }
  
    .product-gallery__thumbnail-list-wrapper > button:first-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: 8px;
    }
  
    .product-gallery__thumbnail-list-wrapper > button:first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: 8px;
    }
  
    .product-gallery__thumbnail-list-wrapper > button:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: 8px;
    }
  
    .product-gallery__thumbnail-list-wrapper > button:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: 8px;
    }
  
    .product-gallery__thumbnail-list-wrapper > button[disabled] {
      opacity: 0;
    }
  
    .product-gallery__thumbnail-list-wrapper:hover > button:not([disabled]) {
      opacity: 1;
      transform: scale(1);
    }
  
    .product-gallery--desktop-grid .product-gallery__thumbnail-list-wrapper {
      display: none;
    }
  
    .product-gallery__media--expand {
      grid-column: span 2;
    }
  
    .product-gallery__zoom {
      opacity: 0;
      position: absolute;
      top: auto;
      bottom: var(--spacing-6);
      transform: scale(.8);
    }
  
    .product-gallery__zoom:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: var(--spacing-6);
    }
  
    .product-gallery__zoom:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: var(--spacing-6);
    }
  
    .product-gallery__zoom svg {
      width: 17px;
      height: 17px;
    }
  
    .product-gallery__zoom > .circle-button {
      width: 3.5rem;
      height: 3.5rem;
    }
  
    .product-gallery--desktop-grid .product-gallery__media:hover .product-gallery__zoom {
      opacity: 1;
      transform: scale(1);
    }
  
    .product-gallery--desktop-carousel .product-gallery__zoom {
      opacity: 1;
      visibility: visible;
    }
  
    .product-gallery--desktop-carousel:has([data-media-type*="video"].is-selected) .product-gallery__zoom {
      opacity: 0;
      visibility: hidden;
    }
  
    [data-shopify-xr-hidden] {
      display: none;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .product-gallery--desktop-thumbnails-left {
      grid: auto / auto-flow auto minmax(0, 1fr);
      align-items: start;
    }
  
    .product-gallery--desktop-thumbnails-left .product-gallery__thumbnail-list-wrapper {
      order: -1;
      grid-auto-flow: row;
      justify-items: center;
      overflow: auto;
    }
  
    .product-gallery--desktop-thumbnails-left .product-gallery__thumbnail-list {
      gap: var(--spacing-2-5);
      grid-auto-flow: row;
      max-height: 600px;
      overflow: auto;
    }
  
    .product-gallery--desktop-thumbnails-left .product-gallery__thumbnail-list-wrapper > button {
      transform: rotate(90deg);
    }
  }
  
  .revealed-image {
    z-index: -1;
    display: inline;
    position: relative;
  }
  
  .revealed-image__scroll-tracker {
    height: 100%;
    position: absolute;
    top: 0;
  }
  
  .revealed-image__scroller {
    height: 180vh;
    height: 180lvh;
    display: block;
    position: sticky;
    top: 0;
    overflow: hidden;
  }
  
  .revealed-image__wrapper {
    height: 100vh;
    top: calc(var(--sticky-area-height) / 2);
    grid-auto-rows: minmax(0, 1fr);
    place-items: center;
    height: 100lvh;
    display: grid;
    position: relative;
  }
  
  .revealed-image__wrapper > * {
    grid-area: 1 / -1;
  }
  
  .revealed-image__wrapper img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }
  
  .revealed-image__wrapper > .revealed-image__image-clipper, .revealed-image__wrapper > .revealed-image__content--inside {
    clip-path: inset(37% 37% 41%);
    width: 100%;
    height: 100%;
  }
  
  .revealed-image__content {
    padding-inline: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
    text-align: center;
    z-index: 1;
    opacity: 0;
    place-content: center;
    width: 100%;
    height: 100%;
    display: grid;
  }
  
  .revealed-image__content-inner {
    max-width: 1300px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  .rich-text {
    display: flex;
  }
  
  .rich-text__wrapper {
    max-width: var(--rich-text-max-width);
  }
  
  .rich-text .prose, .split-rich-text .prose {
    align-items: start;
    display: grid;
  }
  
  .rich-text .image-icon {
    margin-block-end: 0 !important;
  }
  
  .split-rich-text {
    display: none;
  }
  
  @media screen and (min-width: 1000px) {
    .split-rich-text {
      gap: var(--spacing-24);
      grid-template-columns: repeat(2, minmax(0, 1fr));
      display: grid;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .split-rich-text {
      gap: var(--spacing-32);
    }
  }
  
  .scrolling-text {
    overflow: hidden;
  }
  
  .scrolling-text__wrapper {
    display: grid;
  }
  
  .scrolling-text__text {
    line-height: normal;
    font-size: var(--scrolling-text-font-size);
    padding-inline-start: min(1em, 2rem);
    padding-inline-end: min(1em, 2rem);
  }
  
  @supports (overflow: clip) {
    .scrolling-text {
      overflow: clip visible;
    }
  
    .scrolling-text__text {
      line-height: 1;
    }
  }
  
  @media screen and (min-width: 700px) {
    .scrolling-text__text {
      padding-inline-start: min(1.5em, 4rem);
      padding-inline-end: min(1.5em, 4rem);
    }
  }
  
  @media (prefers-reduced-motion: no-preference) {
    .scrolling-text__wrapper {
      grid: auto / auto-flow max-content;
    }
  
    .scrolling-text--auto .scrolling-text__text {
      animation: translateFull var(--marquee-animation-duration, 0s) linear infinite;
    }
  
    .scrolling-text--scroll .scrolling-text__wrapper {
      min-width: min-content;
    }
  }
  
  @media (prefers-reduced-motion: reduce) {
    .scrolling-text {
      --scrolling-text-font-size: var(--text-h0);
    }
  
    .scrolling-text__wrapper {
      text-align: center;
      justify-content: center;
    }
  }
  

  
  .search-input {
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-4);
    border-bottom-width: 2px;
    padding-block-end: var(--spacing-2-5);
    transition: border-bottom-color .1s;
    display: flex;
  }
  
  .search-input:focus-within {
    border-bottom-color: rgb(var(--text-color));
  }
  
  .search-input > input {
    -webkit-appearance: none;
    appearance: none;
    font-size: var(--text-h5);
    background: none;
    border-radius: 0;
    outline: none;
    flex-grow: 1;
    width: 0;
    font-weight: bolder;
  }
  
  .search-input > input::placeholder {
    color: rgb(var(--text-color) / .5);
  }
  
  .search-input > input[type="search"]::-webkit-search-decoration {
    display: none;
  }
  
  .search-input > input[type="search"]::-webkit-search-cancel-button {
    display: none;
  }
  
  .search-input > [type="reset"] {
    font-size: var(--text-sm);
    opacity: 0;
    transition: opacity .2s;
  }
  
  .search-input > input:not(:placeholder-shown) ~ [type="reset"] {
    opacity: 1;
  }
  
  @media screen and (min-width: 700px) {
    .search-input {
      padding-block-end: var(--spacing-3);
    }
  
    .search-input > input {
      font-size: var(--text-h4);
    }
  
    .search-input > [type="reset"] {
      font-size: var(--text-base);
    }
  }
  
  .search-drawer {
    --drawer-body-padding: 1.25rem 1.5rem 1.5rem 1.5rem;
    --drawer-content-max-height: 100%;
    height: 100%;
  }
  
  .search-drawer::part(content) {
    height: max-content;
    overflow: auto;
  }
  
  .search-drawer::part(outside-close-button), .search-drawer::part(close-button) {
    display: none;
  }
  
  @media screen and (min-width: 700px) {
    .search-drawer {
      --drawer-body-padding: 2rem 2.5rem 2.5rem 2.5rem;
    }
  
    .search-drawer::part(content) {
      height: 100%;
    }
  }
  
  .predictive-search {
    min-width: 0;
    display: block;
  }
  
  .predictive-search__tabs {
    gap: var(--spacing-4);
    display: grid;
  }
  
  .predictive-search__tabs::part(tab-list) {
    white-space: nowrap;
    scrollbar-width: none;
    gap: var(--spacing-4);
    margin-inline: calc(-1 * var(--container-outer-width));
    padding-inline: var(--container-outer-width);
    scroll-padding-inline: var(--container-outer-width);
    grid-auto-columns: max-content;
    grid-auto-flow: column;
    display: grid;
    overflow: auto hidden;
  }
  
  .predictive-search__tab-item {
    align-items: center;
    gap: var(--spacing-2);
    display: flex;
  }
  
  .predictive-search__tab-item [aria-selected] {
    transition: opacity .2s ease-in-out;
  }
  
  .predictive-search__tab-item [aria-selected="false"]:not(:hover) {
    opacity: .3;
  }
  
  .predictive-search-result {
    align-items: center;
    gap: var(--spacing-5);
    display: flex;
  }
  
  .predictive-search-result > img {
    flex-shrink: 0;
    width: 5rem;
  }
  
  @media screen and (min-width: 700px) {
    .predictive-search__tabs {
      gap: var(--spacing-6);
    }
  
    .predictive-search__tabs::part(tab-list) {
      gap: var(--spacing-5);
    }
  
    .predictive-search-result {
      gap: var(--spacing-6);
    }
  
    .predictive-search-result > img {
      width: 6rem;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .search-drawer--full {
      --search-full-gap: 40px;
      width: 100%;
    }
  
    .search-drawer--full .search-input {
      padding-block-end: var(--spacing-6);
    }
  
    .search-drawer--full .predictive-search__tabs {
      gap: var(--spacing-8);
    }
  
    .search-drawer--full .predictive-search__tabs::part(tab-list), .search-drawer--full .predictive-search__tabs::part(tab-panels), .search-drawer--full .predictive-search__skeleton-full-width {
      grid-template-columns: repeat(var(--predictive-search-column-count, 4), minmax(var(--predictive-search-column-width, 0px), 1fr));
      gap: 0 var(--search-full-gap);
      display: grid;
    }
  
    .search-drawer--full .predictive-search__tab-content {
      display: grid !important;
    }
  
    .search-drawer--full .predictive-search__tab-item {
      justify-content: space-between;
      align-items: center;
      display: flex;
    }
  
    .search-drawer--full .predictive-search__tab-content {
      align-content: start;
    }
  
    .search-drawer--full .predictive-search__tab-content ~ .predictive-search__tab-content:before {
      content: "";
      margin-left: calc(-1 * var(--search-full-gap) / 2);
      background: rgb(var(--text-color) / .12);
      width: 1px;
      height: 100%;
      position: absolute;
      top: 0;
    }
  
    .search-drawer--full [role="tab"] {
      pointer-events: none;
      opacity: 1 !important;
    }
  }
  
  @media screen and (min-width: 1400px) {
    .search-drawer--full {
      --search-full-gap: 96px;
    }
  }
  
  .main-search-form .search-input {
    width: 260px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  @media screen and (min-width: 700px) {
    .main-search-form .search-input {
      width: 490px;
    }
  }
  
  .shop-the-look {
    --shop-the-look-gap: var(--spacing-5);
    --shop-the-look-grid: auto-flow / auto;
    grid: var(--shop-the-look-grid);
    align-items: flex-start;
    gap: var(--shop-the-look-gap);
    display: grid;
  }
  
  .shop-the-look__dot {
    --dot-size: 10px;
    top: calc(var(--shop-the-look-dot-top)  - (var(--dot-size) / 2));
    left: calc(var(--shop-the-look-dot-left)  - (var(--dot-size) / 2));
    width: var(--dot-size);
    height: var(--dot-size);
    border-radius: var(--rounded-full);
    background-color: rgb(var(--shop-the-look-dot-background));
    transition: transform .2s ease-in-out;
    position: absolute;
  }
  
  .shop-the-look__dot:after {
    content: "";
    top: calc(50% + (var(--spacing-6) * -1));
    left: calc(50% + (var(--spacing-6) * -1));
    width: var(--spacing-12);
    height: var(--spacing-12);
    background: radial-gradient(50% 50% at 50% 50%, rgb(var(--shop-the-look-dot-background) / 0), rgb(var(--shop-the-look-dot-background) / .3));
    border-radius: var(--rounded-full);
    animation: 2s ease-in-out infinite alternate ping;
    position: absolute;
  }
  
  .shop-the-look__dot[aria-current="true"] {
    transform: scale(1.3);
  }
  
  .shop-the-look__products {
    display: grid;
  }
  
  .shop-the-look__carousel > .horizontal-product-list {
    grid: var(--shop-the-look-carousel-grid);
    display: grid;
  }
  
  @media screen and (pointer: fine) {
    .shop-the-look__dot:hover {
      transform: scale(1.3);
    }
  }
  
  @media screen and (min-width: 700px) {
    .section-boxed .shop-the-look {
      --shop-the-look-gap: var(--calculated-section-spacing-inline);
      --shop-the-look-grid: auto / 55% minmax(0, 1fr);
    }
  
    .shop-the-look {
      --shop-the-look-gap: var(--spacing-18);
      --shop-the-look-grid: auto / minmax(0, 1fr) 40%;
    }
  
    .shop-the-look__products {
      justify-items: flex-end;
      gap: var(--spacing-8);
      display: grid;
    }
  
    .shop-the-look__carousel {
      width: 100%;
      display: grid;
    }
  
    .shop-the-look__carousel > * {
      grid-area: 1 / -1;
    }
  
    .shop-the-look__controls {
      gap: var(--spacing-4);
      grid: auto / auto-flow;
      margin-inline-start: auto;
      margin-inline-end: auto;
      display: grid;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .section-boxed .shop-the-look__carousel {
      --shop-the-look-carousel-width: 100%;
    }
  
    .shop-the-look {
      --shop-the-look-gap: 0;
      --shop-the-look-grid: auto / repeat(2, minmax(0, 1fr));
    }
  
    .shop-the-look__products {
      justify-items: center;
    }
  
    .shop-the-look__carousel {
      --shop-the-look-carousel-width: 60%;
      width: var(--shop-the-look-carousel-width);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .section-boxed .shop-the-look {
      --shop-the-look-grid: auto / repeat(2, 1fr);
    }
  
    .section-boxed .shop-the-look__carousel {
      --shop-the-look-carousel-width: 60%;
    }
  }
  
  .slideshow {
    background: var(--slideshow-background);
  }
  
  .slideshow, .slideshow__carousel {
    color: rgb(var(--text-color));
    display: block;
    position: relative;
  }
  
  .slideshow__slide {
    position: relative;
  }
  
  .slideshow__slide:not(.is-selected) {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  .slideshow__slide-background {
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  .slideshow__slide video-media ~ * {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
  }
  
  .slideshow__slide .content-over-media:before {
    background: rgb(var(--content-over-media-overlay)) !important;
  }
  
  .slideshow__controls {
    z-index: 1;
    position: absolute;
    bottom: var(--container-gutter);
  }
  
  .slideshow__controls:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
  }
  
  .slideshow__controls:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
  }
  
  .slideshow__controls .circle-button--fill {
    background: rgb(var(--slideshow-controls-background));
    color: rgb(var(--slideshow-controls-color));
  }
  
  .slideshow__controls .circle-button--bordered {
    color: rgb(var(--slideshow-controls-color));
  }
  
  .slideshow__controls .numbered-dots > * {
    color: rgb(var(--slideshow-controls-color) / .7);
    border-color: rgb(var(--slideshow-controls-color) / .3);
  }
  
  .slideshow__controls .numbered-dots > [aria-current="true"] {
    color: rgb(var(--slideshow-controls-color));
  }
  
  .slideshow__controls .stretching-dots > * {
    background: rgb(var(--slideshow-controls-color));
  }
  
  .slideshow__cursor.is-half-start .icon-chevron-right {
    transform: rotate(180deg);
  }
  
  .slideshow__cursor-ring {
    --radius: 27;
    --circumference: calc(2px * (22 / 7) * var(--radius));
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .slideshow__cursor-ring circle {
    stroke-opacity: 1;
    stroke-dashoffset: 0;
    stroke-dasharray: calc(var(--circumference) * var(--progress, 0)), var(--circumference);
    transform: rotate(-90deg);
  }
  
  .slideshow .content-over-media--auto svg {
    height: min(700px, 50vmax);
  }
  
  @media screen and (max-width: 699px) {
    .slideshow__controls .circle-button {
      width: var(--spacing-10);
      height: var(--spacing-10);
    }
  
    .slideshow--multiple-slides :is(.place-self-end-start, .place-self-end-center) {
      padding-block-end: 2.5rem;
    }
  }
  
  .slideshow--boxed {
    --content-over-media-gap: var(--section-inner-spacing-inline);
    background: var(--slideshow-background);
    padding-block-start: var(--section-inner-max-spacing-block);
    padding-block-end: var(--section-inner-max-spacing-block);
    padding-inline-start: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
    padding-inline-end: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
  }
  
  .slideshow--boxed .content-over-media > :not(img, svg) {
    /* padding-block-start: 0 !important; */
  }
  
  .shopify-section:first-child .slideshow--boxed[allow-transparent-header] {
    padding-block-start: max(var(--section-inner-max-spacing-block), var(--header-height));
  }
  
  @media screen and (min-width: 1400px) {
    .slideshow__controls {
      bottom: var(--spacing-14);
    }
  
    .slideshow__controls:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: var(--spacing-14);
    }
  
    .slideshow__controls:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: var(--spacing-14);
    }
  }
  
  .shopify-payment-button__button {
    height: auto !important;
  }
  
  .shopify-payment-button__button[disabled] {
    opacity: 1 !important;
  }
  
  .shopify-payment-button__button--branded {
    border-radius: var(--rounded-button) !important;
    min-height: 100% !important;
    overflow: hidden !important;
  }
  
  .shopify-payment-button__button--unbranded {
    --button-outline-color: var(--button-background, --button-background-primary);
    padding: var(--shopify-payment-button-padding, var(--spacing-4) var(--spacing-8)) !important;
    -webkit-appearance: none !important;
    border-radius: var(--rounded-button) !important;
    background: rgb(var(--button-background, var(--button-background-primary)) / var(--button-background-opacity, 1)) !important;
    color: rgb(var(--button-text-color, var(--button-text-primary))) !important;
    font-family: var(--text-font-family) !important;
    font-style: var(--text-font-style) !important;
    font-weight: bolder !important;
    font-size: var(--shopify-payment-button-font-size, var(--text-base)) !important;
    text-align: center !important;
    line-height: inherit !important;
    letter-spacing: var(--text-letter-spacing) !important;
    text-decoration: none !important;
    transition: background-color .15s ease-in-out, color .15s ease-in-out, box-shadow .15s ease-in-out !important;
    display: inline-block !important;
    position: relative !important;
    box-shadow: inset 0 0 0 2px #0000 !important;
  }
  
  shopify-accelerated-checkout, shopify-accelerated-checkout-cart {
    --shopify-accelerated-checkout-button-block-size: 54px;
    --shopify-accelerated-checkout-button-border-radius: var(--rounded-button);
    --shopify-accelerated-checkout-button-box-shadow: none;
  }
  
  @media screen and (min-width: 700px) {
    .shopify-payment-button__button--unbranded {
      padding: var(--shopify-payment-button-padding, 1.075rem var(--spacing-10)) !important;
    }
  
    shopify-accelerated-checkout, shopify-accelerated-checkout-cart {
      --shopify-accelerated-checkout-button-block-size: 60px;
    }
  }
  
  .shopify-policy__container {
    gap: var(--spacing-12) !important;
    padding: var(--spacing-14) 0 !important;
    grid-auto-columns: minmax(0, 1fr) !important;
    max-width: none !important;
    margin-inline-start: max(var(--container-gutter), 50% - 80ch / 2) !important;
    margin-inline-end: max(var(--container-gutter), 50% - 80ch / 2) !important;
    display: grid !important;
  }
  
  .shopify-challenge__container, .shopify-email-marketing-confirmation__container {
    padding: var(--spacing-14) 0 !important;
  }
  
  @media screen and (min-width: 700px) {
    .shopify-policy__container, .shopify-challenge__container, .shopify-email-marketing-confirmation__container {
      /* padding-block-start: var(--spacing-16) !important; */
      padding-block-end: var(--spacing-16) !important;
    }
  }
  
  .tabs {
    max-width: var(--tabs-max-width);
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: grid;
  }
  
  @media screen and (max-width: 699px) {
    .tabs-inner {
      display: none;
    }
  }
  
  @media screen and (min-width: 700px) {
    .tabs-inner {
      gap: var(--spacing-10);
      display: grid;
    }
  
    .tabs-inner::part(tab-list) {
      box-shadow: inset 0 -2px rgb(var(--text-color) / .1);
      grid: auto / auto-flow 1fr;
      align-items: end;
      display: grid;
      position: relative;
    }
  
    .tabs-inner::part(tab-list):after {
      content: "";
      width: calc(100% / var(--item-count));
      background: rgb(var(--text-color));
      transform: translateX(calc(var(--selected-index) * var(--transform-logical-flip) * 100%));
      height: 2px;
      transition: transform .3s;
      position: absolute;
      bottom: 0;
    }
  
    .tabs-inner::part(tab-list):not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
      left: 0;
    }
  
    .tabs-inner::part(tab-list):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
      right: 0;
    }
  
    .tabs-nav__button {
      padding-block-start: var(--spacing-6);
      padding-block-end: var(--spacing-6);
      transition: color .2s ease-in-out;
    }
  
    .tabs-nav__button[aria-selected="true"] {
      color: rgb(var(--text-color));
    }
  }
  
  .testimonial-list {
    grid: var(--testimonial-list-grid);
    gap: var(--grid-gutter);
    align-items: start;
    display: grid;
  }
  
  .testimonial {
    align-content: start;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    scroll-snap-align: center;
    scroll-snap-stop: always;
    display: grid;
  }
  
  .testimonial__image {
    width: var(--spacing-10);
  }
  
  @media screen and (min-width: 700px) {
    .testimonial {
      gap: var(--spacing-5);
      padding: var(--spacing-8);
    }
  
    .testimonial__image {
      width: var(--spacing-14);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .testimonial {
      scroll-snap-align: none;
    }
  }
  
  @media screen and (min-width: 1400px) {
    .testimonial {
      gap: var(--spacing-6);
      padding: var(--spacing-10);
    }
  }
  
  .text-with-icons {
    gap: var(--spacing-8);
    text-align: var(--text-with-icons-text-align);
    justify-content: var(--text-with-icons-justify);
    display: grid;
  }
  
  .text-with-icons__list {
    grid: auto / var(--text-with-icons-template);
    row-gap: var(--spacing-6);
    align-items: start;
    min-width: 0;
    display: grid;
  }
  
  .text-with-icons__item {
    gap: var(--text-with-icons-gap);
    justify-items: var(--text-with-icons-justify);
    padding-inline-start: var(--spacing-6);
    padding-inline-end: var(--spacing-6);
    display: grid;
  }
  
  @media screen and (min-width: 1000px) {
    :not(.is-scrollable) .text-with-icons__list {
      gap: var(--spacing-6);
    }
  
    :not(.is-scrollable) .text-with-icons__item {
      padding-inline-start: 0;
      padding-inline-end: 0;
    }
  }
  
  @media screen and (min-width: 1400px) {
    :not(.is-scrollable) .text-with-icons__list {
      gap: var(--spacing-10);
    }
  }
  
  .timeline {
    gap: var(--spacing-8);
    grid-template-columns: minmax(0, 1fr);
    display: grid;
  }
  
  .timeline__slider {
    align-items: start;
    display: grid;
  }
  
  .timeline__slider > * {
    grid-area: 1 / -1;
  }
  
  .timeline__slide {
    gap: var(--spacing-6);
    text-align: center;
    grid-template-columns: minmax(0, 1fr);
    align-items: start;
    display: grid;
  }
  
  .timeline__image {
    width: 100%;
    max-width: 410px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    position: relative;
  }
  
  .timeline__controls {
    --timeline-dot-size: var(--spacing-4);
    --timeline-dot-padding-inline-end: var(--spacing-4);
    padding-block-start: calc((var(--timeline-dot-size) / 2));
  }
  
  .timeline__nav {
    grid: auto / repeat(var(--timeline-nav-item-count), minmax(0, 1fr));
    width: max-content;
    min-width: 100%;
    display: grid;
  }
  
  .timeline__nav-bar {
    background-color: rgb(var(--text-color) / .12);
    width: 100%;
    height: 2px;
    position: absolute;
    top: -1px;
  }
  
  .timeline__nav-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .timeline__nav-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .timeline__nav-item {
    gap: var(--spacing-3);
    align-content: flex-start;
    min-width: 100px;
    max-width: 200px;
    padding-inline-end: var(--timeline-dot-padding-inline-end);
    display: grid;
    position: relative;
  }
  
  .timeline__nav-item:before {
    content: "";
    width: var(--timeline-dot-size);
    height: var(--timeline-dot-size);
    border-radius: var(--rounded-full);
    background-color: rgb(var(--background));
    border-width: 2px;
    margin-block-start: calc((var(--timeline-dot-size) / 2) * -1);
    transition: border-color .2s ease-in-out, background-color .2s ease-in-out;
  }
  
  .timeline__nav-item[aria-current="true"]:before {
    border-color: rgb(var(--text-color));
    background-color: rgb(var(--text-color));
  }
  
  .timeline__nav-label {
    opacity: .5;
    transition: opacity .2s ease-in-out;
  }
  
  .timeline__nav-item[aria-current="true"] .timeline__nav-label {
    opacity: 1;
  }
  
  @media screen and (min-width: 700px) {
    .timeline {
      gap: var(--spacing-14);
    }
  
    .timeline__slide {
      gap: var(--spacing-10);
    }
  
    .timeline__slide > .prose {
      padding-inline-start: var(--spacing-14);
      padding-inline-end: var(--spacing-14);
    }
  
    .timeline__content {
      gap: var(--spacing-6);
    }
  
    .timeline__controls {
      --timeline-dot-size: var(--spacing-5);
      --timeline-dot-padding-inline-end: var(--spacing-5);
    }
  
    .timeline__nav-item {
      min-width: 160px;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .timeline__slide {
      text-align: left;
      grid: auto / repeat(2, minmax(0, 1fr));
      gap: 0;
    }
  
    .timeline__slide > .timeline__image-wrapper {
      padding-inline-end: 16.5%;
    }
  
    .timeline__slide > .prose {
      z-index: 1;
      padding-inline-start: 0;
      padding-inline-end: 0;
      position: relative;
    }
  
    .timeline__image {
      max-width: initial;
    }
  
    .timeline__image-wrapper {
      position: relative;
    }
  
    .timeline__controls {
      align-items: flex-start;
      gap: var(--spacing-10);
      display: flex;
    }
  
    .timeline__controls.scroll-area {
      overflow: visible;
    }
  
    .timeline__buttons {
      gap: var(--spacing-4);
      margin-block-start: calc(var(--spacing-6) * -1);
      display: flex;
    }
  
    .timeline__nav {
      width: 100%;
      min-width: auto;
      padding-block-start: 0;
    }
  
    .timeline__nav-item {
      min-width: auto;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .timeline__slide > .prose {
      padding-inline-end: 16.5%;
    }
  
    .timeline__content {
      gap: var(--spacing-8);
    }
  }
  
  @media screen and (min-width: 1400px) {
    .timeline__slide > .prose {
      max-width: 85%;
    }
  }
  
  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }
  
  :is(.sr-only[type="checkbox"]:focus-visible + label, .sr-only[type="radio"]:focus-visible + label) {
    outline: 2px solid highlight;
    outline: 2px solid -webkit-focus-ring-color;
  }
  
  .skip-to-content:focus-visible {
    z-index: 9999;
    clip: auto;
    color: #fff;
    background: #000;
    width: auto;
    height: auto;
    padding: 6px 12px;
    font-weight: bold;
    overflow: auto;
  }
  
  .tap-area {
    position: relative;
  }
  
  .tap-area:before {
    content: "";
    inset: calc(-1 * var(--spacing-2-5));
    position: absolute;
  }
  
  @media print {
    .print\:hidden {
      display: none;
    }
  }
  
  @keyframes translateFull {
    from {
      transform: translateX(0);
    }
  
    to {
      transform: translateX(calc(-100% * var(--transform-logical-flip)));
    }
  }
  
  @keyframes ping {
    from {
      transform: scale(1);
    }
  
    to {
      transform: scale(.8);
    }
  }
  
  @media screen and (pointer: fine) {
    .zoom-image--enabled .zoom-image {
      transform-origin: center;
      transition: transform 1.5s cubic-bezier(.22, 1, .36, 1);
    }
  
    .zoom-image--enabled .group:hover .zoom-image {
      transform: scale(1.06);
    }
  }
  
  .reveal {
    opacity: 0;
  }
  
  .reveal-invisible {
    opacity: 0;
    visibility: hidden;
  }
  
  @media (prefers-reduced-motion: no-preference) {
    [reveal-js], [reveal-on-scroll="true"] {
      opacity: 0;
    }
  }
  
  @media (scripting: none) {
    [reveal-js], [reveal-on-scroll="true"] {
      opacity: 1;
    }
  }
  
  @media (prefers-reduced-motion: no-preference) {
    reveal-items {
      opacity: var(--stagger-products-reveal-opacity);
    }
  }
  
  @media (scripting: none) {
    reveal-items {
      opacity: 1;
    }
  }
  
  .border {
    border-width: 1px;
  }
  
  .border-x {
    border-left-width: 1px;
    border-right-width: 1px;
  }
  
  .border-y {
    border-block-start-width: 1px;
    border-block-end-width: 1px;
  }
  
  .border-t {
    border-top-width: 1px;
  }
  
  .border-b {
    border-bottom-width: 1px;
  }
  
  .border-s {
    border-block-start-width: 1px;
  }
  
  .border-e {
    border-block-end-width: 1px;
  }
  
  .divide-x > :not([hidden]) ~ :not([hidden]) {
    border-inline-start-width: 1px;
  }
  
  .divide-y > :not([hidden]) ~ :not([hidden]) {
    border-block-start-width: 1px;
  }
  
  .ring {
    box-shadow: 0 0 0 1px rgb(var(--text-color) / .12);
  }
  
  .ring-current {
    box-shadow: 0 0 0 1px rgb(var(--text-color));
  }
  
  .ring-inset {
    box-shadow: inset 0 0 0 1px rgb(var(--text-color) / .12);
  }
  
  .rounded-xs {
    border-radius: var(--rounded-xs);
  }
  
  .rounded-sm {
    border-radius: var(--rounded-sm);
  }
  
  .rounded {
    border-radius: var(--rounded);
  }
  
  .rounded-lg {
    border-radius: var(--rounded-lg);
  }
  
  .rounded-full {
    border-radius: var(--rounded-full);
  }
  
  .text-custom {
    color: rgb(var(--text-color));
  }
  
  .text-accent {
    color: rgb(var(--accent));
  }
  
  .text-success {
    color: rgb(var(--success-text));
  }
  
  .text-warning {
    color: rgb(var(--warning-text));
  }
  
  .text-error {
    color: rgb(var(--error-text));
  }
  
  .text-subdued {
    color: rgb(var(--text-color) / .7);
  }
  
  .text-on-sale {
    color: rgb(var(--on-sale-text));
  }
  
  .text-stroke {
    -webkit-text-stroke: var(--text-stroke-width, 1px) var(--text-stroke-color, currentColor);
    -webkit-text-fill-color: transparent;
  }
  
  .text-gradient {
    background-image: var(--gradient);
    color: #0000;
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
    -webkit-background-clip: text;
    background-clip: text;
  }
  
  .text-stroke.text-gradient {
    color: rgb(var(--background));
    -webkit-text-stroke-color: transparent;
    -webkit-text-fill-color: unset;
  }
  
  .bg-custom {
    background: rgb(var(--background));
  }
  
  .bg-gradient {
    background-image: var(--gradient);
  }
  
  .bg-text {
    background: rgb(var(--text-color));
  }
  
  .bg-error {
    background: rgb(var(--error-background));
  }
  
  .bg-success {
    background: rgb(var(--success-background));
  }
  
  .bg-warning {
    background: rgb(var(--warning-background));
  }
  
  .bg-secondary {
    background: rgb(var(--text-color) / .05);
  }
  
  .opacity-0 {
    opacity: 0;
  }
  
  .backdrop-blur {
    -webkit-backdrop-filter: blur(var(--backdrop-blur, 0px));
    backdrop-filter: blur(var(--backdrop-blur, 0px));
  }
  
  .filter-invert {
    filter: invert();
  }
  
  .shadow-sm {
    filter: drop-shadow(var(--shadow-sm));
  }
  
  .shadow {
    filter: drop-shadow(var(--shadow));
  }
  
  .shadow-md {
    filter: drop-shadow(var(--shadow-md));
  }
  
  .shadow-block {
    box-shadow: var(--shadow-block);
  }
  
  .cross-fade {
    display: block;
  }
  
  .cross-fade > * {
    transition: opacity .2s ease-in-out;
  }
  
  .cross-fade > :last-child {
    display: none;
  }
  
  @media screen and (pointer: fine) {
    .cross-fade > :last-child {
      opacity: 0;
      display: block;
    }
  
    .cross-fade:hover > :first-child {
      opacity: 0;
    }
  
    .cross-fade:hover > :last-child {
      opacity: 1;
    }
  }
  
  .pointer-events-none {
    pointer-events: none;
  }
  
  .scroll-area {
    scrollbar-width: none;
    scroll-snap-type: x mandatory;
    overscroll-behavior-x: contain;
    overflow: auto hidden;
  }
  
  .scroll-area::-webkit-scrollbar {
    display: none;
  }
  
  .snap-start {
    scroll-snap-align: start;
    scroll-snap-stop: always;
  }
  
  .snap-center {
    scroll-snap-align: center;
    scroll-snap-stop: always;
  }
  
  :not(.is-scrollable) ~ .peer-not-scrollable\:hidden, .hidden, .empty\:hidden:empty {
    display: none;
  }
  
  .inline-block {
    display: inline-block;
  }
  
  .block {
    display: block;
  }
  
  .contents {
    display: contents;
  }
  
  @media screen and (min-width: 700px) {
    .sm\:hidden {
      display: none;
    }
  
    .sm\:block {
      display: block;
    }
  
    .sm\:flex {
      display: flex;
    }
  
    .sm\:grid {
      display: grid;
    }
  
    .sm\:table-cell {
      display: table-cell;
    }
  
    .sm\:contents {
      display: contents;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .md\:hidden {
      display: none;
    }
  
    .md\:block {
      display: block;
    }
  
    .md\:flex {
      display: flex;
    }
  
    .md\:grid {
      display: grid;
    }
  
    .md\:table-cell {
      display: table-cell;
    }
  
    .md\:contents {
      display: contents;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .lg\:hidden {
      display: none;
    }
  
    .lg\:block {
      display: block;
    }
  
    .lg\:flex {
      display: flex;
    }
  
    .lg\:grid {
      display: grid;
    }
  
    .lg\:table-cell {
      display: table-cell;
    }
  
    .lg\:contents {
      display: contents;
    }
  }
  
  @media screen and (min-width: 1400px) {
    .xl\:hidden {
      display: none;
    }
  
    .xl\:block {
      display: block;
    }
  
    .xl\:flex {
      display: flex;
    }
  
    .xl\:grid {
      display: grid;
    }
  
    .xl\:table-cell {
      display: table-cell;
    }
  
    .xl\:contents {
      display: contents;
    }
  }
  
  @media screen and (min-width: 1600px) {
    .\32 xl\:hidden {
      display: none;
    }
  
    .\32 xl\:block {
      display: block;
    }
  
    .\32 xl\:flex {
      display: flex;
    }
  
    .\32 xl\:grid {
      display: grid;
    }
  
    .\32 xl\:table-cell {
      display: table-cell;
    }
  
    .\32 xl\:contents {
      display: contents;
    }
  }
  
  @media screen and (max-width: 699px) {
    .sm-max\:hidden {
      display: none;
    }
  }
  
  @media screen and (max-width: 999px) {
    .md-max\:hidden {
      display: none;
    }
  }
  
  @media screen and (max-width: 1149px) {
    .lg-max\:hidden {
      display: none;
    }
  }
  
  @media screen and (max-width: 1399px) {
    .xl-max\:hidden {
      display: none;
    }
  }
  
  @media screen and (max-width: 1599px) {
    .\32 xl-max\:hidden {
      display: none;
    }
  }
  
  @media screen and (pointer: fine) {
    .pointer-fine\:hidden {
      display: none;
    }
  
    .pointer-fine\:block {
      display: block;
    }
  }
  
  @media not screen and (pointer: fine) {
    .pointer-coarse\:hidden {
      display: none;
    }
  
    .pointer-coarse\:block {
      display: block;
    }
  }
  
  @media (prefers-reduced-motion: reduce) {
    .motion-reduce\:hidden {
      display: none;
    }
  
    .motion-reduce\:block {
      display: block;
    }
  }
  
  .invisible {
    visibility: hidden;
  }
  
  .relative {
    position: relative;
  }
  
  .wrap {
    flex-wrap: wrap;
  }
  
  .grow {
    flex-grow: 1;
  }
  
  .shrink-0 {
    flex-shrink: 0;
  }
  
  .text-start {
    text-align: start;
  }
  
  .text-center {
    text-align: center;
  }
  
  .text-end {
    text-align: end;
  }
  
  .justify-start {
    justify-content: start;
  }
  
  .justify-center {
    justify-content: safe center;
  }
  
  .justify-end {
    justify-content: end;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  .justify-evenly {
    justify-content: space-evenly;
  }
  
  .justify-self-start {
    justify-self: start;
  }
  
  .justify-self-center {
    justify-self: center;
  }
  
  .justify-self-end {
    justify-self: end;
  }
  
  .justify-items-start {
    justify-items: start;
  }
  
  .justify-items-center {
    justify-items: safe center;
  }
  
  .justify-items-end {
    justify-items: end;
  }
  
  .align-start {
    align-items: start;
  }
  
  .align-center {
    align-items: center;
  }
  
  .align-end {
    align-items: end;
  }
  
  .align-self-start {
    align-self: start;
  }
  
  .align-self-center {
    align-self: center;
  }
  
  .align-self-end {
    align-self: end;
  }
  
  .align-self-stretch {
    align-self: stretch;
  }
  
  .place-self-start {
    place-self: start;
  }
  
  .place-self-start-center {
    place-self: start center;
  }
  
  .place-self-start-end {
    place-self: start end;
  }
  
  .place-self-center {
    place-self: center;
  }
  
  .place-self-center-start {
    place-self: center start;
  }
  
  .place-self-center-end {
    place-self: center end;
  }
  
  .place-self-end {
    place-self: end;
  }
  
  .place-self-end-start {
    place-self: end start;
  }
  
  .place-self-end-center {
    place-self: end center;
  }
  
  @media screen and (min-width: 700px) {
    .sm\:text-start {
      text-align: start;
    }
  
    .sm\:text-center {
      text-align: center;
    }
  
    .sm\:text-end {
      text-align: end;
    }
  
    .sm\:place-self-start {
      place-self: start;
    }
  
    .sm\:place-self-start-center {
      place-self: start center;
    }
  
    .sm\:place-self-start-end {
      place-self: start end;
    }
  
    .sm\:place-self-center {
      place-self: center;
    }
  
    .sm\:place-self-center-start {
      place-self: center start;
    }
  
    .sm\:place-self-center-end {
      place-self: center end;
    }
  
    .sm\:place-self-end {
      place-self: end;
    }
  
    .sm\:place-self-end-start {
      place-self: end start;
    }
  
    .sm\:place-self-end-center {
      place-self: end center;
    }
  
    .sm\:justify-items-center {
      justify-items: safe center;
    }
  }
  
  .align-top {
    vertical-align: top;
  }
  
  .align-center {
    vertical-align: middle;
  }
  
  .align-bottom {
    vertical-align: bottom;
  }
  
  .w-full {
    width: 100%;
  }
  
  .h-full {
    height: 100%;
  }
  
  .w-0 {
    width: 0;
  }
  
  .min-w-full {
    min-width: 100%;
  }
  
  .min-h-full {
    min-height: 100%;
  }
  
  .mx-auto {
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  .my-auto {
    margin-block-start: auto;
    margin-block-end: auto;
  }
  
  .aspect-short {
    aspect-ratio: 4 / 3;
  }
  
  .aspect-tall {
    aspect-ratio: 2 / 3;
  }
  
  .aspect-square {
    aspect-ratio: 1;
  }
  
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-video iframe {
    width: 100%;
    height: 100%;
  }
  
  .gap-0\.5 {
    gap: var(--spacing-0-5);
  }
  
  .gap-1 {
    gap: var(--spacing-1);
  }
  
  .gap-1\.5 {
    gap: var(--spacing-1-5);
  }
  
  .gap-2 {
    gap: var(--spacing-2);
  }
  
  .gap-2\.5 {
    gap: var(--spacing-2-5);
  }
  
  .gap-3 {
    gap: var(--spacing-3);
  }
  
  .gap-3\.5 {
    gap: var(--spacing-3-5);
  }
  
  .gap-4 {
    gap: var(--spacing-4);
  }
  
  .gap-4\.5 {
    gap: var(--spacing-4-5);
  }
  
  .gap-5 {
    gap: var(--spacing-5);
  }
  
  .gap-5\.5 {
    gap: var(--spacing-5-5);
  }
  
  .gap-6 {
    gap: var(--spacing-6);
  }
  
  .gap-8 {
    gap: var(--spacing-8);
  }
  
  .gap-10 {
    gap: var(--spacing-10);
  }
  
  .gap-12 {
    gap: var(--spacing-12);
  }
  
  @media screen and (min-width: 700px) {
    .sm\:gap-0\.5 {
      gap: var(--spacing-0-5);
    }
  
    .sm\:gap-1 {
      gap: var(--spacing-1);
    }
  
    .sm\:gap-1\.5 {
      gap: var(--spacing-1-5);
    }
  
    .sm\:gap-2 {
      gap: var(--spacing-2);
    }
  
    .sm\:gap-2\.5 {
      gap: var(--spacing-2-5);
    }
  
    .sm\:gap-3 {
      gap: var(--spacing-3);
    }
  
    .sm\:gap-3\.5 {
      gap: var(--spacing-3-5);
    }
  
    .sm\:gap-4 {
      gap: var(--spacing-4);
    }
  
    .sm\:gap-4\.5 {
      gap: var(--spacing-4-5);
    }
  
    .sm\:gap-5 {
      gap: var(--spacing-5);
    }
  
    .sm\:gap-5\.5 {
      gap: var(--spacing-5-5);
    }
  
    .sm\:gap-6 {
      gap: var(--spacing-6);
    }
  
    .sm\:gap-8 {
      gap: var(--spacing-8);
    }
  
    .sm\:gap-10 {
      gap: var(--spacing-10);
    }
  
    .sm\:gap-12 {
      gap: var(--spacing-12);
    }
  }
  
  .overflow-hidden {
    overflow: hidden;
  }
  
  .object-fill {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }
  
  .object-fill:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .object-fill:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .object-fill, .object-fill-safe, .object-cover {
    object-fit: cover;
    object-position: center;
  }
  
  .object-contain {
    object-fit: contain;
    object-position: center;
  }
  
  .placeholder {
    background: rgb(var(--text-primary) / .3);
    fill: rgb(var(--text-primary) / .7);
    color: rgb(var(--text-primary) / .7);
    display: block;
  }
  
  .bold {
    font-weight: bold;
  }
  
  .text-xxs {
    font-size: 10px;
    line-height: 1.7;
  }
  
  .text-xs {
    font-size: var(--text-xs);
    line-height: 1.7;
  }
  
  .text-sm {
    font-size: var(--text-sm);
    line-height: 1.6;
  }
  
  .text-base {
    font-size: var(--text-base);
    line-height: 1.6;
  }
  
  .text-lg {
    font-size: var(--text-lg);
    line-height: 1.6;
  }
  
  .subheading {
    font-family: var(--accent-font-family);
    font-weight: var(--accent-font-weight);
    letter-spacing: var(--accent-font-spacing);
    text-transform: uppercase;
  }
  
  .accent {
    font-family: var(--accent-font-family);
    font-weight: var(--accent-font-weight);
    letter-spacing: var(--accent-font-spacing);
    text-transform: uppercase;
  }
  
  .heading, .h0, .h1, .h2, .h3, .h4, .h5, .h6, .prose :is(h1, h2, h3, h4, h5, h6) {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    font-style: var(--heading-font-style);
    letter-spacing: var(--heading-letter-spacing);
    text-transform: var(--heading-text-transform);
    overflow-wrap: anywhere;
  }
  
  .h0 {
    font-size: var(--text-h0);
    line-height: 0.9;
  }
  
  .h1, .prose h1:not(.h0, .h1, .h2, .h3, .h4, .h5, .h6) {
    font-size: var(--text-h1);
    line-height: 0.9;
  }
  
  .h2, .prose h2:not(.h0, .h1, .h2, .h3, .h4, .h5, .h6) {
    font-size: var(--text-h2);
    line-height: 1.1;
  }
  
  .h3, .prose h3:not(.h0, .h1, .h2, .h3, .h4, .h5, .h6) {
    font-size: var(--text-h3);
    line-height: 1.2;
  }
  
  .h4, .prose h4:not(.h0, .h1, .h2, .h3, .h4, .h5, .h6) {
    font-size: var(--text-h4);
    line-height: 1.3;
  }
  
  .h5, .prose h5:not(.h0, .h1, .h2, .h3, .h4, .h5, .h6) {
    font-size: var(--text-h5);
    line-height: 1.4;
  }
  
  .h6, .prose h6:not(.h0, .h1, .h2, .h3, .h4, .h5, .h6) {
    font-size: var(--text-h6);
    line-height: 1.4;
  }
  
  @media screen and (min-width: 700px) {
    .sm\:text-xxs {
      font-size: 10px;
      line-height: 1.7;
    }
  
    .sm\:text-xs {
      font-size: var(--text-xs);
      line-height: 1.7;
    }
  
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: 1.6;
    }
  
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: 1.6;
    }
  
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: 1.6;
    }
  
    .sm\:h0 {
      font-size: var(--text-h0);
      line-height: 1;
    }
  
    .sm\:h1 {
      font-size: var(--text-h1);
      line-height: 1.1;
    }
  
    .sm\:h2 {
      font-size: var(--text-h2);
      line-height: 1.1;
    }
  
    .sm\:h3 {
      font-size: var(--text-h3);
      line-height: 1.2;
    }
  
    .sm\:h4 {
      font-size: var(--text-h4);
      line-height: 1.3;
    }
  
    .sm\:h5 {
      font-size: var(--text-h5);
      line-height: 1.4;
    }
  
    .sm\:h6 {
      font-size: var(--text-h6);
      line-height: 1.4;
    }
  }
  
  .line-through {
    text-decoration: line-through;
  }
  
  .break-all {
    overflow-wrap: anywhere;
  }
  
  .hyphenate {
    -webkit-hyphens: auto;
    hyphens: auto;
  }
  
  .truncate-text {
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    overflow: hidden;
  }
  
  .line-clamp {
    -webkit-line-clamp: var(--line-clamp-count, 2);
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }
  