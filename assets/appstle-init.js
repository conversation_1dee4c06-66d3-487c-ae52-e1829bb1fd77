document.addEventListener('DOMContentLoaded', function() {
  // Initialize the Appstle subscription widget if available.
  if (typeof appstleInit === 'function') {
    appstleInit();
  }

  // Define border colors.
  const fullOpacityBorderColor = 'rgb(var(--text-color))';        // Full opacity border.
  const halfOpacityBorderColor = 'rgb(var(--text-color) / 0.2)';      // 50% opacity border.

  // When selected:
  // - All options get full opacity border,
  // - Then non-dropdown ones are overridden to half opacity.
  function setSelectedStyles() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.setProperty('border-color', fullOpacityBorderColor, 'important');
    });
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown)').forEach(option => {
      option.style.setProperty('border-color', halfOpacityBorderColor, 'important');
    });
  }

  // When deselected:
  // - All options get half opacity border,
  // - Then non-dropdown ones are overridden to full opacity.
  function setDeselectedStyles() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.setProperty('border-color', halfOpacityBorderColor, 'important');
    });
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown)').forEach(option => {
      option.style.setProperty('border-color', fullOpacityBorderColor, 'important');
    });
  }

  // Helper: clear background colors from all options.
  function clearBackgroundColors() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.removeProperty('background-color');
    });
  }

  // Event: When a subscription plan is selected.
  document.addEventListener('AppstleSubscription:SubscriptionWidget:SellingPlanSelected', function() {
    const checkedInput = document.querySelector('.appstle_sub_widget input[name=selling_plan]:checked');
    console.log('Selling plan selected:', checkedInput ? checkedInput.value : null);

    // Apply selected border styles.
    setSelectedStyles();
    // Clear previous background colors.
    clearBackgroundColors();

    // Set background color on the selected option's container.
    if (checkedInput) {
      const selectedWrapper = checkedInput.closest('.appstle_subscription_wrapper_option');
      if (selectedWrapper) {
        selectedWrapper.style.setProperty('background-color', '#DDDDDD', 'important');
      }
    }
  });

  // Event: When a subscription plan is deselected.
  document.addEventListener('AppstleSubscription:SubscriptionWidget:SellingPlanDeSelected', function() {
    const checkedInput = document.querySelector('.appstle_sub_widget input[name=selling_plan]:checked');
    console.log('Selling plan deselected:', checkedInput ? checkedInput.value : null);

    // Apply deselected border styles.
    setDeselectedStyles();

    // Instead of clearing all backgrounds, update them:
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      // For non-dropdown options, set background to #DDDDDD.
      if (option.matches(':not(.appstle_include_dropdown)')) {
        option.style.setProperty('background-color', '#DDDDDD', 'important');
      } else {
        // For other options, clear any background color.
        option.style.removeProperty('background-color');
      }
    });
  });

  // Function to update Appstle pricing display
  function updateAppstlePricing() {
    // Find all elements that contain $69 in the Appstle widget
    const appstleWidget = document.querySelector('.appstle_sub_widget');
    if (!appstleWidget) return;

    // Look for any element containing prices that need to be updated
    const elementsToCheck = appstleWidget.querySelectorAll('*');

    elementsToCheck.forEach(element => {
      const text = element.textContent.trim();

      // Skip if already processed or contains multiple prices (our modified elements)
      if (element.hasAttribute('data-price-updated') ||
          text.includes('$55') ||
          text.includes('$49') ||
          element.querySelector('span[style*="line-through"]')) {
        return;
      }

      // Check if this element contains exactly $69 and hasn't been modified yet (BUY ONCE option)
      if (text === '$69') {
        // Mark as updated to prevent infinite loop
        element.setAttribute('data-price-updated', 'true');

        // Replace the content
        element.innerHTML = '$55 <span style="text-decoration: line-through; color: #666; margin-left: 4px;">$69</span>';
      }

      // Check if this element contains exactly $63 and hasn't been modified yet (SUBSCRIBE AND SAVE option)
      else if (text === '$63') {
        // Mark as updated to prevent infinite loop
        element.setAttribute('data-price-updated', 'true');

        // Replace with $49 first, then strikethrough $63
        element.innerHTML = '$49 <span style="text-decoration: line-through; color: #666; margin-left: 4px;">$63</span>';
      }

      // Check if this element contains exactly $0 and hasn't been modified yet (remove the $0)
      else if (text === '$0') {
        // Mark as updated to prevent infinite loop
        element.setAttribute('data-price-updated', 'true');

        // Remove the $0 by making it empty
        element.innerHTML = '';
      }
    });
  }

  // Smart mutation observer that avoids infinite loops
  let isUpdating = false;
  let updateTimeout;

  const observer = new MutationObserver(function(mutations) {
    if (isUpdating) return; // Prevent infinite loops

    // Clear any pending updates
    if (updateTimeout) {
      clearTimeout(updateTimeout);
    }

    // Check if any mutation contains original Appstle prices (not our modified ones)
    const hasRelevantChanges = mutations.some(mutation => {
      if (mutation.type === 'childList') {
        return Array.from(mutation.addedNodes).some(node => {
          if (node.textContent) {
            const text = node.textContent.trim();
            // Only trigger for original Appstle prices, not our modified ones
            return (text === '$69' || text === '$63' || text === '$0') &&
                   !text.includes('$55') && !text.includes('$49');
          }
          return false;
        });
      }
      return false;
    });

    if (hasRelevantChanges) {
      // Debounce the update to prevent multiple rapid calls
      updateTimeout = setTimeout(() => {
        isUpdating = true;
        updateAppstlePricing();
        setTimeout(() => { isUpdating = false; }, 200);
      }, 100);
    }
  });

  // Check for widget and start observing
  const checkForWidget = setInterval(() => {
    const appstleWidget = document.querySelector('.appstle_sub_widget');
    if (appstleWidget) {
      updateAppstlePricing();
      observer.observe(appstleWidget, {
        childList: true,
        subtree: true
      });
      clearInterval(checkForWidget);
    }
  }, 100);
});