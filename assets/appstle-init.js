document.addEventListener('DOMContentLoaded', function() {
  // Initialize the Appstle subscription widget if available.
  if (typeof appstleInit === 'function') {
    appstleInit();
  }

  // Define border colors.
  const fullOpacityBorderColor = 'rgb(var(--text-color))';        // Full opacity border.
  const halfOpacityBorderColor = 'rgb(var(--text-color) / 0.2)';      // 50% opacity border.

  // When selected:
  // - All options get full opacity border,
  // - Then non-dropdown ones are overridden to half opacity.
  function setSelectedStyles() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.setProperty('border-color', fullOpacityBorderColor, 'important');
    });
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown)').forEach(option => {
      option.style.setProperty('border-color', halfOpacityBorderColor, 'important');
    });
  }

  // When deselected:
  // - All options get half opacity border,
  // - Then non-dropdown ones are overridden to full opacity.
  function setDeselectedStyles() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.setProperty('border-color', halfOpacityBorderColor, 'important');
    });
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown)').forEach(option => {
      option.style.setProperty('border-color', fullOpacityBorderColor, 'important');
    });
  }

  // Helper: clear background colors from all options.
  function clearBackgroundColors() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.removeProperty('background-color');
    });
  }

  // Event: When a subscription plan is selected.
  document.addEventListener('AppstleSubscription:SubscriptionWidget:SellingPlanSelected', function() {
    const checkedInput = document.querySelector('.appstle_sub_widget input[name=selling_plan]:checked');
    console.log('Selling plan selected:', checkedInput ? checkedInput.value : null);

    // Apply selected border styles.
    setSelectedStyles();
    // Clear previous background colors.
    clearBackgroundColors();

    // Set background color on the selected option's container.
    if (checkedInput) {
      const selectedWrapper = checkedInput.closest('.appstle_subscription_wrapper_option');
      if (selectedWrapper) {
        selectedWrapper.style.setProperty('background-color', '#DDDDDD', 'important');
      }
    }
  });

  // Event: When a subscription plan is deselected.
  document.addEventListener('AppstleSubscription:SubscriptionWidget:SellingPlanDeSelected', function() {
    const checkedInput = document.querySelector('.appstle_sub_widget input[name=selling_plan]:checked');
    console.log('Selling plan deselected:', checkedInput ? checkedInput.value : null);

    // Apply deselected border styles.
    setDeselectedStyles();

    // Instead of clearing all backgrounds, update them:
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      // For non-dropdown options, set background to #DDDDDD.
      if (option.matches(':not(.appstle_include_dropdown)')) {
        option.style.setProperty('background-color', '#DDDDDD', 'important');
      } else {
        // For other options, clear any background color.
        option.style.removeProperty('background-color');
      }
    });
  });

  // Function to update Appstle pricing display - run only once
  function updateAppstlePricing() {
    // Find all elements that contain $69 in the Appstle widget
    const appstleWidget = document.querySelector('.appstle_sub_widget');
    if (!appstleWidget) return;

    // Look for any element containing exactly $69 that hasn't been updated yet
    const allElements = appstleWidget.querySelectorAll('*');

    allElements.forEach(element => {
      // Check if this element contains exactly $69 and hasn't been modified yet (BUY ONCE option)
      if (element.textContent.trim() === '$69' && !element.hasAttribute('data-price-updated')) {
        // Mark as updated to prevent infinite loop
        element.setAttribute('data-price-updated', 'true');

        // Replace the content
        element.innerHTML = '$55 <span style="text-decoration: line-through; color: #666; margin-left: 4px;">$69</span>';
      }

      // Check if this element contains exactly $63 and hasn't been modified yet (SUBSCRIBE AND SAVE option)
      if (element.textContent.trim() === '$63' && !element.hasAttribute('data-price-updated')) {
        // Mark as updated to prevent infinite loop
        element.setAttribute('data-price-updated', 'true');

        // Replace with $49 first, then strikethrough $63
        element.innerHTML = '$49 <span style="text-decoration: line-through; color: #666; margin-left: 4px;">$63</span>';
      }

      // Check if this element contains exactly $0 and hasn't been modified yet (remove the $0)
      if (element.textContent.trim() === '$0' && !element.hasAttribute('data-price-updated')) {
        // Mark as updated to prevent infinite loop
        element.setAttribute('data-price-updated', 'true');

        // Remove the $0 by making it empty
        element.innerHTML = '';
      }
    });
  }

  // Smart mutation observer that avoids infinite loops
  let isUpdating = false;

  const observer = new MutationObserver(function(mutations) {
    if (isUpdating) return; // Prevent infinite loops

    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' || mutation.type === 'characterData') {
        // Check if the mutation contains price-related changes
        const hasRelevantChanges = Array.from(mutation.addedNodes).some(node => {
          return node.textContent && (node.textContent.includes('$') || node.textContent.includes('0'));
        });

        if (hasRelevantChanges) {
          setTimeout(() => {
            isUpdating = true;
            updateAppstlePricing();
            setTimeout(() => { isUpdating = false; }, 100);
          }, 50);
        }
      }
    });
  });

  // Check for widget and start observing
  const checkForWidget = setInterval(() => {
    const appstleWidget = document.querySelector('.appstle_sub_widget');
    if (appstleWidget) {
      updateAppstlePricing();
      observer.observe(appstleWidget, {
        childList: true,
        subtree: true,
        characterData: true
      });
      clearInterval(checkForWidget);
    }
  }, 100);
});