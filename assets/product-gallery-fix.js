/**
 * Product Gallery Mobile Fix
 *
 * This script fixes issues with the product gallery on mobile devices:
 * - Fixes scrolling/swiping issues
 * - Ensures navigation buttons work properly
 * - Prevents touch event conflicts
 * - Fixes navigation arrow visibility
 */

document.addEventListener('DOMContentLoaded', function() {
  // Fix for mobile product gallery
  function fixMobileProductGallery() {
    const galleries = document.querySelectorAll('product-gallery');
    const isMobile = window.innerWidth < 1000;

    galleries.forEach(gallery => {
      // Get the carousel element
      const carousel = gallery.querySelector('media-carousel');
      if (!carousel) return;

      // Fix for mobile touch events
      if (isMobile) {
        // Remove any existing event listeners that might be conflicting
        carousel._abortController?.abort();
        carousel._abortController = new AbortController();

        // Ensure the carousel has proper touch handling for horizontal swiping
        carousel.style.touchAction = 'pan-x';

        // Fix for snap scrolling on mobile
        const mediaItems = carousel.querySelectorAll('.product-gallery__media');
        mediaItems.forEach(item => {
          item.style.scrollSnapAlign = 'center';

          // Ensure media items are properly sized and visible
          const mediaImage = item.querySelector('img');
          if (mediaImage) {
            mediaImage.style.maxHeight = '100%';
            mediaImage.style.objectFit = 'contain';
          }
        });

        // Ensure navigation buttons work on mobile
        const prevButton = gallery.querySelector('.js-prev-button');
        const nextButton = gallery.querySelector('.js-next-button');

        if (prevButton && nextButton) {
          // Clean up existing listeners
          prevButton.replaceWith(prevButton.cloneNode(true));
          nextButton.replaceWith(nextButton.cloneNode(true));

          // Get fresh references
          const newPrevButton = gallery.querySelector('.js-prev-button');
          const newNextButton = gallery.querySelector('.js-next-button');

          // Add new event listeners
          newPrevButton?.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // First, remove any existing classes that might affect color
            this.classList.remove('clicked', 'reset');

            // Force reflow to ensure clean state
            void this.offsetWidth;

            // Add clicked class to show it was pressed
            this.classList.add('clicked');

            // Remove the clicked class and add reset class after a short delay
            setTimeout(() => {
              this.classList.remove('clicked');

              // Force reflow again
              void this.offsetWidth;

              // Add reset class to ensure it goes back to original color
              this.classList.add('reset');

              // Finally remove the reset class after the transition completes
              setTimeout(() => {
                this.classList.remove('reset');

                // Explicitly set the background color back to original
                this.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
              }, 300);
            }, 300);

            carousel.previous();
            updateButtonVisibility(gallery, carousel);
          }, { signal: carousel._abortController.signal });

          newNextButton?.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // First, remove any existing classes that might affect color
            this.classList.remove('clicked', 'reset');

            // Force reflow to ensure clean state
            void this.offsetWidth;

            // Add clicked class to show it was pressed
            this.classList.add('clicked');

            // Remove the clicked class and add reset class after a short delay
            setTimeout(() => {
              this.classList.remove('clicked');

              // Force reflow again
              void this.offsetWidth;

              // Add reset class to ensure it goes back to original color
              this.classList.add('reset');

              // Finally remove the reset class after the transition completes
              setTimeout(() => {
                this.classList.remove('reset');

                // Explicitly set the background color back to original
                this.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
              }, 300);
            }, 300);

            carousel.next();
            updateButtonVisibility(gallery, carousel);
          }, { signal: carousel._abortController.signal });

          // Add scroll event listener to update button visibility
          carousel.addEventListener('scroll', function() {
            // Debounce the scroll event to avoid too many updates
            clearTimeout(carousel._scrollTimeout);
            carousel._scrollTimeout = setTimeout(() => {
              updateButtonVisibility(gallery, carousel);
            }, 50);
          }, { passive: true, signal: carousel._abortController.signal });

          // Update on touchend to ensure correct state after swipe
          carousel.addEventListener('touchend', function() {
            // Wait for scroll to settle before updating
            setTimeout(() => updateButtonVisibility(gallery, carousel), 100);
          }, { passive: true, signal: carousel._abortController.signal });

          // Add additional event listeners for better tracking
          carousel.addEventListener('carousel:change', function() {
            updateButtonVisibility(gallery, carousel);
          }, { signal: carousel._abortController.signal });

          carousel.addEventListener('carousel:settle', function() {
            updateButtonVisibility(gallery, carousel);
          }, { signal: carousel._abortController.signal });

          // Track scrollend event (newer browsers)
          if ('onscrollend' in window) {
            carousel.addEventListener('scrollend', function() {
              updateButtonVisibility(gallery, carousel);
            }, { passive: true, signal: carousel._abortController.signal });
          }

          // Initial update
          updateButtonVisibility(gallery, carousel);
        }

        // Fix for dots navigation if present
        const pageDots = gallery.querySelectorAll('page-dots button');
        pageDots.forEach((dot, index) => {
          dot.addEventListener('click', function(e) {
            e.preventDefault();
            carousel.select(index);
            updateButtonVisibility(gallery, carousel);
          }, { signal: carousel._abortController.signal });
        });
      }
    });
  }

  // Function to update navigation button visibility
  function updateButtonVisibility(gallery, carousel) {
    if (!carousel || !gallery) return;

    // Get all visible slides
    const visibleSlides = Array.from(carousel.querySelectorAll('.product-gallery__media')).filter(item => !item.hidden);
    if (visibleSlides.length <= 1) return; // No need to show navigation if only one slide

    // Find the current slide position using multiple methods to ensure accuracy
    let currentIndex;

    // Method 1: Check for is-selected class
    currentIndex = visibleSlides.findIndex(slide => slide.classList.contains('is-selected'));

    // Method 2: Use scroll position (more reliable for swipe actions)
    // Always calculate scroll position as a backup or primary method
    const scrollLeft = carousel.scrollLeft;
    const carouselWidth = carousel.offsetWidth;
    const scrollIndex = Math.round(scrollLeft / carouselWidth);

    // If is-selected method failed or scroll position is different, use scroll position
    if (currentIndex === -1 || Math.abs(currentIndex - scrollIndex) > 0) {
      currentIndex = scrollIndex;
    }

    // Method 3: Check which slide is most visible in the viewport
    const carouselRect = carousel.getBoundingClientRect();
    const centerX = carouselRect.left + carouselRect.width / 2;

    // Find the slide that's most centered in the viewport
    let bestVisibleIndex = -1;
    let bestVisibleOverlap = -1;

    visibleSlides.forEach((slide, index) => {
      const slideRect = slide.getBoundingClientRect();
      const slideCenter = slideRect.left + slideRect.width / 2;
      const distanceFromCenter = Math.abs(centerX - slideCenter);

      // The slide with the smallest distance from center is most visible
      if (bestVisibleIndex === -1 || distanceFromCenter < bestVisibleOverlap) {
        bestVisibleIndex = index;
        bestVisibleOverlap = distanceFromCenter;
      }
    });

    // If we found a clearly visible slide, use that
    if (bestVisibleIndex !== -1 && Math.abs(bestVisibleIndex - currentIndex) <= 1) {
      currentIndex = bestVisibleIndex;
    }

    // Ensure index is within bounds
    currentIndex = Math.max(0, Math.min(currentIndex, visibleSlides.length - 1));

    // Store the current index as a data attribute for future reference
    carousel.dataset.currentIndex = currentIndex;

    // Update the is-selected class to match the current index
    // This ensures the class-based and scroll-based methods stay in sync
    visibleSlides.forEach((slide, index) => {
      if (index === currentIndex) {
        if (!slide.classList.contains('is-selected')) {
          slide.classList.add('is-selected');
        }
      } else {
        slide.classList.remove('is-selected');
      }
    });

    // Get the current buttons
    const prevButton = gallery.querySelector('.js-prev-button');
    const nextButton = gallery.querySelector('.js-next-button');

    if (prevButton && nextButton) {
      // Update visibility based on current position
      if (currentIndex <= 0) {
        prevButton.style.display = 'none';
        nextButton.style.display = '';
      } else if (currentIndex >= visibleSlides.length - 1) {
        prevButton.style.display = '';
        nextButton.style.display = 'none';
      } else {
        prevButton.style.display = '';
        nextButton.style.display = '';
      }

      // Force the buttons to be visible in the DOM
      prevButton.style.opacity = '1';
      nextButton.style.opacity = '1';
      prevButton.style.visibility = 'visible';
      nextButton.style.visibility = 'visible';

      // Reset any lingering classes or styles
      prevButton.classList.remove('clicked', 'reset');
      nextButton.classList.remove('clicked', 'reset');

      // Ensure the background color is reset to original
      prevButton.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
      nextButton.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
    }
  }

  // Function to update all product galleries on the page
  function updateAllGalleries() {
    fixMobileProductGallery();

    // Also manually update button visibility for all galleries
    const galleries = document.querySelectorAll('product-gallery');
    galleries.forEach(gallery => {
      const carousel = gallery.querySelector('media-carousel');
      if (carousel) {
        updateButtonVisibility(gallery, carousel);
      }
    });
  }

  // Run the fix on page load
  updateAllGalleries();

  // Re-run when window is resized
  let resizeTimer;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(updateAllGalleries, 250);
  });

  // Re-run when Shopify sections are reloaded
  document.addEventListener('shopify:section:load', updateAllGalleries);

  // Ensure the fix is applied after the DOM is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', updateAllGalleries);
  } else {
    updateAllGalleries();
  }

  // Also run after images load to ensure proper sizing
  window.addEventListener('load', updateAllGalleries);

  // Add a periodic check to ensure button visibility stays in sync
  setInterval(updateAllGalleries, 2000);

  // Only prevent gesture events that would interfere with horizontal scrolling
  document.addEventListener('gesturestart', function(e) {
    // Only prevent default for non-horizontal gestures
    const carousel = e.target.closest('media-carousel');
    if (carousel) {
      // We'll let the browser handle horizontal gestures
      // but prevent other gestures that might interfere
      if (e.scale !== undefined && (e.scale > 1.1 || e.scale < 0.9)) {
        e.preventDefault();
      }
    }
  }, { passive: false });

  document.addEventListener('touchstart', function(e) {
    const carousel = e.target.closest('media-carousel');
    if (carousel && window.innerWidth < 1000) {
      // Ensure we don't have conflicting touch handlers
      carousel.style.touchAction = 'pan-x';
    }
  }, { passive: true });

  // Add specific swipe handling
  document.addEventListener('DOMContentLoaded', function() {
    const carousels = document.querySelectorAll('media-carousel');
    carousels.forEach(carousel => {
      // Make sure we're not preventing default scroll behavior
      carousel.addEventListener('touchmove', function(e) {
        // Allow default touch behavior for horizontal scrolling
        e.stopPropagation();
      }, { passive: true });

      // Add manual swipe detection
      let startX, startY;
      let threshold = 50; // Minimum distance to be considered a swipe

      carousel.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      }, { passive: true });

      carousel.addEventListener('touchend', function(e) {
        if (!startX || !startY) return;

        let endX = e.changedTouches[0].clientX;
        let endY = e.changedTouches[0].clientY;

        let diffX = startX - endX;
        let diffY = startY - endY;

        // Always update button visibility after any touch interaction
        // This ensures buttons are updated even for small swipes
        const gallery = carousel.closest('product-gallery');
        if (gallery) {
          // Use setTimeout to let the scroll settle first
          setTimeout(() => updateButtonVisibility(gallery, carousel), 50);
        }

        // If horizontal swipe is more significant than vertical
        if (Math.abs(diffX) > Math.abs(diffY)) {
          // If the swipe was significant enough
          if (Math.abs(diffX) > threshold) {
            const gallery = carousel.closest('product-gallery');
            if (gallery) {
              if (diffX > 0) {
                // Swiped left - go to next slide
                carousel.next();

                // Show visual feedback on the next button
                const nextButton = gallery.querySelector('.js-next-button');
                if (nextButton) {
                  // First, remove any existing classes that might affect color
                  nextButton.classList.remove('clicked', 'reset');

                  // Force reflow to ensure clean state
                  void nextButton.offsetWidth;

                  // Add clicked class to show it was pressed
                  nextButton.classList.add('clicked');

                  // Remove the clicked class and add reset class after a short delay
                  setTimeout(() => {
                    nextButton.classList.remove('clicked');

                    // Force reflow again
                    void nextButton.offsetWidth;

                    // Add reset class to ensure it goes back to original color
                    nextButton.classList.add('reset');

                    // Finally remove the reset class after the transition completes
                    setTimeout(() => {
                      nextButton.classList.remove('reset');

                      // Explicitly set the background color back to original
                      nextButton.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
                    }, 300);
                  }, 300);
                }
              } else {
                // Swiped right - go to previous slide
                carousel.previous();

                // Show visual feedback on the prev button
                const prevButton = gallery.querySelector('.js-prev-button');
                if (prevButton) {
                  // First, remove any existing classes that might affect color
                  prevButton.classList.remove('clicked', 'reset');

                  // Force reflow to ensure clean state
                  void prevButton.offsetWidth;

                  // Add clicked class to show it was pressed
                  prevButton.classList.add('clicked');

                  // Remove the clicked class and add reset class after a short delay
                  setTimeout(() => {
                    prevButton.classList.remove('clicked');

                    // Force reflow again
                    void prevButton.offsetWidth;

                    // Add reset class to ensure it goes back to original color
                    prevButton.classList.add('reset');

                    // Finally remove the reset class after the transition completes
                    setTimeout(() => {
                      prevButton.classList.remove('reset');

                      // Explicitly set the background color back to original
                      prevButton.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
                    }, 300);
                  }, 300);
                }
              }

              // Update button visibility after swipe with multiple checks
              // First immediate update
              updateButtonVisibility(gallery, carousel);

              // Then check again after scroll has settled
              setTimeout(() => updateButtonVisibility(gallery, carousel), 100);

              // And one final check to ensure everything is in sync
              setTimeout(() => updateButtonVisibility(gallery, carousel), 300);
            }
          }
        }

        // Reset start position
        startX = null;
        startY = null;
      }, { passive: true });
    });
  });
});
