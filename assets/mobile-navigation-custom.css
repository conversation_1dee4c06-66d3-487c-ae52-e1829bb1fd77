/* Custom styles for mobile navigation menu */

/* These styles are now handled by mobile-menu-animation.js */
/* This file is kept for reference only */
/* Styles for the slide-up animation */
@media screen and (max-width: 699px) {
  /* Override the default drawer behavior for mobile */
  .navigation-drawer[mobile-opening="bottom"]::part(content) {
    border-radius: 16px 16px 0 0 !important;
    max-height: 85vh !important;
    height: auto !important;
  }

  /* Add a subtle shadow to the drawer */
  .navigation-drawer[mobile-opening="bottom"]::part(content) {
    box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.15) !important;
  }

  /* Improve overlay fade-in/out animation and add blur effect */
  .navigation-drawer::part(overlay) {
    -webkit-backdrop-filter: blur(0px);
    backdrop-filter: blur(0px);
    background: rgba(0, 0, 0, 0.4) !important;
    transition: opacity 0.3s ease-out, backdrop-filter 0.3s ease-out, -webkit-backdrop-filter 0.3s ease-out !important;
  }

  .navigation-drawer[open]::part(overlay) {
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
  }

  /* Target the navigation menu items */
  .panel__wrapper .panel__scroller .h3.sm\:h4 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
  }

  /* Target the social media icons in the footer */
  .panel-footer .social-media {
    transform: scale(1.0);
    transform-origin: left center;
  }

  /* Make social media icons larger */
  .panel-footer .social-media .icon-instagram,
  .panel-footer .social-media .icon-tiktok {
    width: 32px;
    height: 32px;
  }

  /* Add padding to all social media links for a larger tap target */
  .panel-footer .social-media li a.tap-area {
    padding: 8px;
    margin: -8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  Reduce spacing in the navigation panel
  .panel__scroller.v-stack.gap-8 {
    gap: var(--spacing-5) !important;
  }

  /* Reduce spacing in the panel footer */
  .panel-footer.v-stack.gap-5 {
    gap: var(--spacing-3) !important;
  }

  /* Reduce the size of the navigation drawer close button
  .navigation-drawer [is="close-button"] {
    width: var(--spacing-8) !important;
    height: var(--spacing-8) !important;
    min-height: var(--spacing-8) !important;
  } */

  Adjust the SVG icon size inside the close button
  .navigation-drawer [is="close-button"] svg {
    width: 16px !important;
    height: 16px !important;
  }
}
