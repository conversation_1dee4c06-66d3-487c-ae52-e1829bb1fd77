/**
 * Product Gallery Mobile Fixes
 *
 * CSS fixes for the product gallery on mobile devices
 */

/* Fix for mobile product gallery */
@media screen and (max-width: 999px) {
  /* Ensure proper scrolling behavior */
  .product-gallery__media-list {
    scroll-snap-type: x mandatory;
    overflow-x: auto;
    scroll-behavior: smooth;
    /* Hide scrollbar but keep functionality */
    scrollbar-width: none;
    -ms-overflow-style: none;
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
    /* Prevent text selection during swipe */
    -webkit-user-select: none;
    user-select: none;
    /* Ensure horizontal scrolling works */
    white-space: nowrap;
    display: flex;
    flex-wrap: nowrap;
  }

  .product-gallery__media-list::-webkit-scrollbar {
    display: none;
  }

  /* Ensure media items snap properly */
  .product-gallery__media {
    scroll-snap-align: center;
    flex: 0 0 100%;
    width: 100%;
    height: auto;
    position: relative;
    display: inline-block;
    vertical-align: top;
  }

  /* Ensure images are properly sized */
  .product-gallery__media img {
    max-height: 100%;
    object-fit: contain;
    width: 100%;
  }

  /* Make navigation buttons more tappable */
  .product-gallery__nav-arrow {
    min-width: 44px;
    min-height: 44px;
    z-index: 5;
    touch-action: manipulation;
  }

  /* Ensure dots are tappable */
  .product-gallery--mobile-dots .page-dots__item {
    min-width: 30px;
    min-height: 30px;
    touch-action: manipulation;
  }

  /* Fix for potential z-index issues */
  .product-gallery__media-list-wrapper {
    position: relative;
    z-index: 1;
  }

  /* Ensure proper height for carousel */
  media-carousel {
    min-height: 300px;
  }
}

/* Fix for navigation buttons */
.product-gallery__nav-arrow {
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 10 !important;
  background-color: rgba(255, 255, 255, 0.5) !important; /* Restore original semi-transparent white */
}

/* Ensure proper positioning of navigation arrows */
.product-gallery__nav-arrow--prev {
  left: 0 !important; /* Restore original position */
  border-radius: 0 50% 50% 0 !important; /* Restore original shape */
  padding: 0 !important;
  justify-content: flex-start !important;
}

.product-gallery__nav-arrow--next {
  right: 0 !important; /* Restore original position */
  border-radius: 50% 0 0 50% !important; /* Restore original shape */
  padding: 0 !important;
  justify-content: flex-end !important;
}

/* Restore original margin for buttons */
.product-gallery__nav-arrow--prev .circle-button {
  margin-left: 3px !important;
}

.product-gallery__nav-arrow--next .circle-button {
  margin-right: 3px !important;
}

/* Button state styling - explicitly set the original color */
.product-gallery__nav-arrow {
  transition: background-color 0.3s ease !important;
  background-color: rgba(255, 255, 255, 0.5) !important; /* Semi-transparent white - original color */
}

/* Remove hover effect to prevent lingering green */
.product-gallery__nav-arrow:hover {
  /* No hover effect to avoid lingering green */
  background-color: rgba(255, 255, 255, 0.5) !important; /* Keep original color on hover */
}

/* Active/pressed state - only show green when actively pressing */
.product-gallery__nav-arrow:active {
  background-color: #47DE47 !important; /* Solid green when actively pressed */
  transition: background-color 0.1s ease !important; /* Faster transition when pressed */
}

/* Clicked state - temporarily green */
.product-gallery__nav-arrow.clicked {
  background-color: #47DE47 !important; /* Solid green when clicked */
  transition: background-color 0.3s ease !important;
}

/* Explicitly define the reset state to ensure it goes back to original */
.product-gallery__nav-arrow.reset {
  background-color: rgba(255, 255, 255, 0.5) !important; /* Force back to original color */
  transition: background-color 0.3s ease !important;
}

/* Remove custom styling for circle button to restore original appearance */
.product-gallery__nav-arrow .circle-button {
  background: transparent !important;
  border: none !important;
  width: 30px !important;
  height: 30px !important;
  box-shadow: none !important;
}

.product-gallery__nav-arrow:hover .circle-button {
  background: transparent !important;
}

/* Fix for potential conflicts with other touch handlers */
@media screen and (max-width: 999px) {
  body, html {
    touch-action: manipulation;
  }

  .product-gallery {
    touch-action: pan-x; /* Allow horizontal swiping */
  }

  /* Ensure the carousel is scrollable horizontally */
  .product-gallery__media-list {
    touch-action: pan-x !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    cursor: grab !important;
  }

  /* Improve touch response */
  .product-gallery__media-list:active {
    cursor: grabbing !important;
  }

  /* Mobile specific adjustments for navigation arrows */
  .product-gallery__nav-arrow {
    width: 35px !important;
    height: 40px !important;
    position: absolute !important;
    z-index: 10 !important;
  }

  .product-gallery__nav-arrow--prev {
    left: 0 !important;
    border-top-right-radius: 20px !important;
    border-bottom-right-radius: 20px !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .product-gallery__nav-arrow--next {
    right: 0 !important;
    border-top-left-radius: 20px !important;
    border-bottom-left-radius: 20px !important;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
}
