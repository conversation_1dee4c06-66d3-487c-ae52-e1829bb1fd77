// Mobile menu animation - direct approach
document.addEventListener('DOMContentLoaded', function() {
  // Add our custom styles
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    @media screen and (max-width: 699px) {
      /* Style for the mobile navigation drawer */
      #header-sidebar-menu::part(content) {
        border-radius: 16px 16px 0 0 !important;
        max-height: 85vh !important;
        box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.15) !important;
        transform: translateY(100%) !important;
        transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1) !important;
      }

      /* When drawer is open, slide it up */
      #header-sidebar-menu[open]::part(content) {
        transform: translateY(0) !important;
      }

      /* Blur effect for overlay */
      #header-sidebar-menu::part(overlay) {
        opacity: 0 !important;
        backdrop-filter: blur(0px) !important;
        -webkit-backdrop-filter: blur(0px) !important;
        transition: opacity 0.4s ease-out, backdrop-filter 0.4s ease-out, -webkit-backdrop-filter 0.4s ease-out !important;
      }

      #header-sidebar-menu[open]::part(overlay) {
        opacity: 1 !important;
        backdrop-filter: blur(4px) !important;
        -webkit-backdrop-filter: blur(4px) !important;
      }

      /* Smaller font size for navigation items */
      .panel__wrapper .panel__scroller .h3.sm\:h4 {
        font-size: 0.95rem !important;
        line-height: 1.3 !important;
      }

      /* Larger social media icons */
      .panel-footer .social-media .icon-instagram,
      .panel-footer .social-media .icon-tiktok {
        width: 32px !important;
        height: 32px !important;
      }

      /* Reduce spacing in the navigation panel */
      .panel__scroller.v-stack.gap-8 {
        gap: var(--spacing-5) !important;
      }

      /* Reduce spacing in the panel footer */
      .panel-footer.v-stack.gap-5 {
        gap: var(--spacing-3) !important;
      }

      /* Reduce the size of the navigation drawer close button */
      .navigation-drawer [is="close-button"] {
        width: var(--spacing-8) !important;
        height: var(--spacing-8) !important;
        min-height: var(--spacing-8) !important;
      }

      /* Adjust the SVG icon size inside the close button */
      .navigation-drawer [is="close-button"] svg {
        width: 16px !important;
        height: 16px !important;
      }
    }
  `;
  document.head.appendChild(styleElement);

  // Function to handle the close button animation
  function setupCloseButtonAnimation() {
    const closeButton = document.querySelector('#mobile-menu-close');
    if (closeButton && !closeButton.hasAttribute('data-animation-setup')) {
      // Mark as processed
      closeButton.setAttribute('data-animation-setup', 'true');

      // Store original click handler
      const originalOnClick = closeButton.onclick;

      // Set new click handler
      closeButton.onclick = function(e) {
        if (window.matchMedia('(max-width: 699px)').matches) {
          e.preventDefault();

          // Get the drawer
          const drawer = document.getElementById('header-sidebar-menu');
          if (drawer) {
            // Get the content part
            const content = drawer.shadowRoot.querySelector('[part="content"]');
            const overlay = drawer.shadowRoot.querySelector('[part="overlay"]');

            if (content) {
              // Set up transition for sliding down
              content.style.transform = 'translateY(0)';

              // Force a reflow
              content.offsetHeight;

              // Animate down
              content.style.transform = 'translateY(100%)';

              if (overlay) {
                overlay.style.opacity = '0';
                overlay.style.backdropFilter = 'blur(0px)';
                overlay.style.webkitBackdropFilter = 'blur(0px)';
              }

              // Wait for animation to complete
              setTimeout(() => {
                // Close the drawer
                drawer.removeAttribute('open');

                // Reset styles
                content.style.transform = '';

                // Call original handler if it exists
                if (typeof originalOnClick === 'function') {
                  originalOnClick.call(this, e);
                }
              }, 400);
            } else {
              // Fallback if we can't find the content
              drawer.removeAttribute('open');
              if (typeof originalOnClick === 'function') {
                originalOnClick.call(this, e);
              }
            }

            return false;
          }
        }

        // For desktop, use original behavior
        if (typeof originalOnClick === 'function') {
          return originalOnClick.call(this, e);
        }
      };
    }
  }

  // Function to set up the mobile menu button
  function setupMobileMenuButton() {
    // Find all mobile menu buttons
    const menuButtons = document.querySelectorAll('.mobile-menu-toggle, [data-action="open-drawer"]');

    menuButtons.forEach(button => {
      if (button && !button.hasAttribute('data-animation-setup') &&
          button.getAttribute('aria-controls') === 'header-sidebar-menu') {

        // Mark as processed
        button.setAttribute('data-animation-setup', 'true');

        // Set mobile-opening attribute on the drawer
        const drawer = document.getElementById('header-sidebar-menu');
        if (drawer) {
          drawer.setAttribute('mobile-opening', 'bottom');
        }
      }
    });
  }

  // Set up a MutationObserver to detect when elements are added to the DOM
  const observer = new MutationObserver((mutations) => {
    let shouldSetupCloseButton = false;
    let shouldSetupMenuButton = false;

    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length) {
        for (const node of mutation.addedNodes) {
          // Check for close button
          if (node.id === 'mobile-menu-close' ||
              (node.querySelector && node.querySelector('#mobile-menu-close'))) {
            shouldSetupCloseButton = true;
          }

          // Check for menu button
          if (node.classList &&
              (node.classList.contains('mobile-menu-toggle') ||
               node.hasAttribute('data-action')) ||
              (node.querySelector &&
               (node.querySelector('.mobile-menu-toggle') ||
                node.querySelector('[data-action="open-drawer"]')))) {
            shouldSetupMenuButton = true;
          }

          // Check for the drawer itself
          if (node.id === 'header-sidebar-menu' ||
              (node.querySelector && node.querySelector('#header-sidebar-menu'))) {
            shouldSetupMenuButton = true;
          }
        }
      }
    }

    if (shouldSetupCloseButton) {
      setupCloseButtonAnimation();
    }

    if (shouldSetupMenuButton) {
      setupMobileMenuButton();
    }
  });

  // Start observing
  observer.observe(document.body, { childList: true, subtree: true });

  // Initial setup
  setupCloseButtonAnimation();
  setupMobileMenuButton();

  // Also set up when the DOM is fully loaded
  window.addEventListener('load', function() {
    setupCloseButtonAnimation();
    setupMobileMenuButton();
    patchNavigationDrawerPrototype();
  });

  // Function to patch the NavigationDrawer prototype
  function patchNavigationDrawerPrototype() {
    try {
      // Wait for the NavigationDrawer component to be defined
      if (customElements.get('navigation-drawer')) {
        const NavigationDrawer = customElements.get('navigation-drawer');

        // Store the original show method
        const originalShow = NavigationDrawer.prototype.show;

        // Override the show method
        NavigationDrawer.prototype.show = function() {
          // Call the original method
          const result = originalShow.apply(this, arguments);

          // Apply our custom animation for mobile
          if (window.matchMedia('(max-width: 699px)').matches && this.id === 'header-sidebar-menu') {
            // Make sure the drawer has the correct mobile-opening attribute
            this.setAttribute('mobile-opening', 'bottom');

            // Access the shadow DOM
            if (this.shadowRoot) {
              const content = this.shadowRoot.querySelector('[part="content"]');
              const overlay = this.shadowRoot.querySelector('[part="overlay"]');

              if (content) {
                // Set initial position
                content.style.transform = 'translateY(100%)';

                // Force a reflow
                content.offsetHeight;

                // Animate to final position
                setTimeout(() => {
                  content.style.transform = 'translateY(0)';
                }, 10);
              }

              if (overlay) {
                overlay.style.opacity = '0';
                overlay.style.backdropFilter = 'blur(0px)';
                overlay.style.webkitBackdropFilter = 'blur(0px)';

                // Force a reflow
                overlay.offsetHeight;

                // Animate overlay
                setTimeout(() => {
                  overlay.style.opacity = '1';
                  overlay.style.backdropFilter = 'blur(4px)';
                  overlay.style.webkitBackdropFilter = 'blur(4px)';
                }, 10);
              }
            }
          }

          return result;
        };

        console.log('Successfully patched NavigationDrawer prototype');
      } else {
        // If not defined yet, wait for it
        customElements.whenDefined('navigation-drawer').then(patchNavigationDrawerPrototype);
      }
    } catch (error) {
      console.error('Error patching NavigationDrawer prototype:', error);
    }
  }

  // Try to patch the prototype immediately
  patchNavigationDrawerPrototype();
});
