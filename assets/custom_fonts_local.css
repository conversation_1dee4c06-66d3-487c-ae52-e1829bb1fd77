/* FormaDJRBanner */
@font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Hairline.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: optional;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-HairlineItalic.woff2') format('woff2');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Thin.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-ThinItalic.woff2') format('woff2');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-ExtraLight.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-ExtraLightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Light.woff2') format('woff2');
    font-weight: 350;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-LightItalic.woff2') format('woff2');
    font-weight: 350;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: fallback;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Italic.woff2') format('woff2');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-MediumItalic.woff2') format('woff2');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-ExtraBold.woff2') format('woff2');
    font-weight: 650;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-ExtraBoldItalic.woff2') format('woff2');
    font-weight: 650;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: fallback;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-BoldItalic.woff2') format('woff2');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-Black.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: fallback;
  }

  @font-face {
    font-family: 'FormaDJRBanner';
    src: url('FormaDJRBanner-BlackItalic.woff2') format('woff2');
    font-weight: 800;
    font-style: italic;
    font-display: swap;
  }

/* FormaDJRDisplay */
@font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Hairline.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-HairlineItalic.woff2') format('woff2');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Thin.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-ThinItalic.woff2') format('woff2');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-ExtraLight.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-ExtraLightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Light.woff2') format('woff2');
    font-weight: 350;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-LightItalic.woff2') format('woff2');
    font-weight: 350;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Italic.woff2') format('woff2');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-MediumItalic.woff2') format('woff2');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-ExtraBold.woff2') format('woff2');
    font-weight: 650;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-ExtraBoldItalic.woff2') format('woff2');
    font-weight: 650;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-BoldItalic.woff2') format('woff2');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-Black.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('FormaDJRDisplay-BlackItalic.woff2') format('woff2');
    font-weight: 800;
    font-style: italic;
    font-display: swap;
  }

/* FormaDJRText */
@font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-Italic.woff2') format('woff2');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-Light.woff2') format('woff2');
    font-weight: 350;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-LightItalic.woff2') format('woff2');
    font-weight: 350;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-ExtraLight.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-ExtraLightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-MediumItalic.woff2') format('woff2');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-BoldItalic.woff2') format('woff2');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-ExtraBold.woff2') format('woff2');
    font-weight: 650;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-ExtraBoldItalic.woff2') format('woff2');
    font-weight: 650;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-Black.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRText';
    src: url('FormaDJRText-BlackItalic.woff2') format('woff2');
    font-weight: 800;
    font-style: italic;
    font-display: swap;
  }

/* FormaDJRDeck */
@font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Italic.woff2') format('woff2');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Hairline.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-HairlineItalic.woff2') format('woff2');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Thin.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-ThinItalic.woff2') format('woff2');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-ExtraLight.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-ExtraLightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Light.woff2') format('woff2');
    font-weight: 350;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-LightItalic.woff2') format('woff2');
    font-weight: 350;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-MediumItalic.woff2') format('woff2');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-BoldItalic.woff2') format('woff2');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-ExtraBold.woff2') format('woff2');
    font-weight: 650;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-ExtraBoldItalic.woff2') format('woff2');
    font-weight: 650;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-Black.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRDeck';
    src: url('FormaDJRDeck-BlackItalic.woff2') format('woff2');
    font-weight: 800;
    font-style: italic;
    font-display: swap;
  }

/* FormaDJRMicro */
@font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-Italic.woff2') format('woff2');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-ExtraLight.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-ExtraLightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-Light.woff2') format('woff2');
    font-weight: 350;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-LightItalic.woff2') format('woff2');
    font-weight: 350;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-MediumItalic.woff2') format('woff2');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-BoldItalic.woff2') format('woff2');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-ExtraBold.woff2') format('woff2');
    font-weight: 650;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-ExtraBoldItalic.woff2') format('woff2');
    font-weight: 650;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-Black.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'FormaDJRMicro';
    src: url('FormaDJRMicro-BlackItalic.woff2') format('woff2');
    font-weight: 800;
    font-style: italic;
    font-display: swap;
  }

/* Meltmino */
@font-face {
  font-family: 'Meltmino';
  src: url('Meltmino-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Meltmino';
  src: url('Meltmino-ExtraLight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Meltmino';
  src: url('Meltmino-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
    font-family: 'Meltmino';
    src: url('Meltmino-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Meltmino';
    src: url('Meltmino-Italic.woff2') format('woff2');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'Meltmino';
    src: url('Meltmino-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Meltmino';
    src: url('Meltmino-MediumItalic.woff2') format('woff2');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }

  @font-face {
    font-family: 'Meltmino';
    src: url('Meltmino-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Meltmino';
    src: url('Meltmino-BoldItalic.woff2') format('woff2');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
  }
