/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "rating": {
          "type": "rating",
          "disabled": true,
          "settings": {
            "show_empty": true,
            "rating_text": "Rated 4.9 | 27,956 Reviews",
            "rating_text_font_family": "FormaDJRDeck",
            "rating_text_size": "text-base",
            "rating_text_weight": 500
          }
        },
        "judge_me_reviews_preview_badge_DEjFad": {
          "type": "shopify://apps/judge-me-reviews/blocks/preview_badge/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        },
        "title": {
          "type": "title",
          "settings": {
            "heading_tag": "h2"
          }
        },
        "price": {
          "type": "price",
          "disabled": true,
          "settings": {
            "price_font_family": "FormaDJRBanner",
            "price_size": "text-xl",
            "price_layout": "inline",
            "show_taxes_notice": false,
            "secondary_text": "30 Servings ($1.67 / serving)",
            "secondary_text_size": "text-sm",
            "secondary_text_alignment": "left"
          }
        },
        "offer_grid_KAzayM": {
          "type": "offer_grid",
          "settings": {
            "desktop_layout": "row",
            "mobile_layout": "stacked",
            "title_font_family": "body",
            "title_font_weight": 500,
            "title_font_size": "text-sm",
            "content_font_family": "body",
            "content_font_weight": 500,
            "content_font_size": "text-sm",
            "background_color": "#dddddd",
            "title_color": "#0d0d0d",
            "content_color": "#0d0d0d",
            "border_radius": "rounded-lg",
            "title_1": "MAGNESIUM",
            "content_1": "<p>300mg</p>",
            "title_2": "SODIUM",
            "content_2": "<p>500mg</p>",
            "title_3": "POTASSIUM",
            "content_3": "<p>250mg</p>"
          }
        },
        "benefits_fQ63tp": {
          "type": "benefits",
          "settings": {
            "title": "",
            "text_1": "<p>improved focus</p>",
            "text_2": "<p>anti-anxiety</p>",
            "text_3": "<p>max hydration</p>",
            "text_4": "<p>anti-stress</p>"
          }
        },
        "badges": {
          "type": "badges",
          "settings": {
            "badge_1_icon": "shopify://shop_images/0_badge2.svg",
            "badge_1_text": "SUGAR",
            "badge_2_icon": "shopify://shop_images/0_badge2.svg",
            "badge_2_text": "GLUTEN",
            "badge_3_icon": "shopify://shop_images/0_badge2.svg",
            "badge_3_text": "FILLERS",
            "badge_4_icon": "shopify://shop_images/0_badge2.svg",
            "badge_4_text": "SOY",
            "badge_5_icon": "shopify://shop_images/0_badge2.svg",
            "badge_5_text": "GMO",
            "badge_6_icon": "shopify://shop_images/0_badge2.svg",
            "badge_6_text": "CAFFEINE",
            "badge_7_icon": "shopify://shop_images/0_badge2.svg",
            "badge_7_text": "CARBS",
            "badge_8_icon": "shopify://shop_images/0_badge2.svg",
            "badge_8_text": "ADDITIVES",
            "badge_font_family": "body",
            "badge_font_size": "text-xxs",
            "badges_layout": "grid"
          }
        },
        "variant_picker_R8HEyM": {
          "type": "variant_picker",
          "settings": {
            "hide_sold_out_variants": false,
            "stack_blocks": false,
            "selector_style": "block",
            "swatch_selector_style": "swatch",
            "variant_image_options": "",
            "size_chart_page": ""
          }
        },
        "buy-buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_payment_button": false,
            "show_gift_card_recipient": true,
            "atc_button_background": "#47de47",
            "atc_button_text_color": "#0d0d0d",
            "payment_button_background": "",
            "payment_button_text_color": ""
          }
        },
        "collapsible_text_6bqhPg": {
          "type": "collapsible_text",
          "disabled": true,
          "settings": {
            "title": "THREE FORMS OF BIOACTIVE MAGNESIUM",
            "content": "<p>Unlike other brands that use magnesium that use Citrate, which helps your bowel movements, our blend is different. It's composed of Glycinate, Taurate, and Malate which are proven to: </p><ul><li>Increase Cellular Efficiency</li><li>Induce a State of Clarity</li><li>Reduces Stress</li><li>Improve Energy Levels </li><li>Improve Sleep</li></ul>",
            "page": ""
          }
        },
        "collapsible_text_yfqYay": {
          "type": "collapsible_text",
          "disabled": true,
          "settings": {
            "title": "OVER 84 IONIC TRACE MINERALS",
            "content": "<p>OS Lytes uses whole-food sourced ingredients in their natural forms. This includes trace minerals including Boron, Selenium, Manganese, Zinc, Phophorus, and more</p><p></p>",
            "page": ""
          }
        },
        "collapsible_text_WYdJxU": {
          "type": "collapsible_text",
          "disabled": true,
          "settings": {
            "title": "NATURALLY SELECTED",
            "content": "<ul><li>No Added Sugars</li><li>No Artificial Food Dyes </li><li>No Artificial Preservatives</li><li>No Fillers</li><li>No Artificial Sweeteners</li></ul>",
            "page": ""
          }
        },
        "text_NY7zVz": {
          "type": "text",
          "settings": {
            "text": "<p>Ships mid-April.<br/>30-day Money Back Guarantee. </p>"
          }
        },
        "description": {
          "type": "description",
          "disabled": true,
          "settings": {
            "collapse_content": false
          }
        },
        "quantity-selector": {
          "type": "quantity_selector",
          "disabled": true,
          "settings": {}
        },
        "appstle_subscription_appstle_subscription_product_page_widget_rm7tej": {
          "type": "shopify://apps/appstle-subscription/blocks/appstle-subscription-product-page-widget/fe257d39-6f88-4f35-ab08-bd437e3aa94e",
          "settings": {}
        }
      },
      "block_order": [
        "rating",
        "judge_me_reviews_preview_badge_DEjFad",
        "title",
        "price",
        "offer_grid_KAzayM",
        "benefits_fQ63tp",
        "badges",
        "variant_picker_R8HEyM",
        "buy-buttons",
        "collapsible_text_6bqhPg",
        "collapsible_text_yfqYay",
        "collapsible_text_WYdJxU",
        "text_NY7zVz",
        "description",
        "quantity-selector",
        "appstle_subscription_appstle_subscription_product_page_widget_rm7tej"
      ],
      "custom_css": [],
      "settings": {
        "full_width": true,
        "show_fixed_add_to_cart": true,
        "desktop_media_width": 50,
        "desktop_media_layout": "carousel_thumbnails_bottom",
        "mobile_media_size": "expanded",
        "mobile_carousel_control": "dots",
        "enable_video_autoplay": true,
        "enable_video_looping": false,
        "enable_image_zoom": false,
        "max_image_zoom_level": 3,
        "background": "#e7e8e5",
        "background_gradient": "",
        "text_color": "",
        "input_background": "rgba(0,0,0,0)",
        "input_text_color": ""
      }
    },
    "ss_comparison_table_12_gwHBPX": {
      "type": "ss-comparison-table-12",
      "blocks": {
        "table_row_w4hQdx": {
          "type": "table_row",
          "settings": {
            "row_heading": "300mg of Magnesium",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_JGPD7n": {
          "type": "table_row",
          "settings": {
            "row_heading": "Reduce Brain Fog",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_pd4bwh": {
          "type": "table_row",
          "settings": {
            "row_heading": "Minimize Bloat",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_PY3VVn": {
          "type": "table_row",
          "settings": {
            "row_heading": "Lower Blood Pressure",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_3ndXYW": {
          "type": "table_row",
          "settings": {
            "row_heading": "Trace Minerals",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_zq9fUB": {
          "type": "table_row",
          "settings": {
            "row_heading": "Fillers",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "none",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_KzM7n7": {
          "type": "table_row",
          "settings": {
            "row_heading": "Sugar",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_TrGQNE": {
          "type": "table_row",
          "settings": {
            "row_heading": "Preservatives",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        }
      },
      "block_order": [
        "table_row_w4hQdx",
        "table_row_JGPD7n",
        "table_row_pd4bwh",
        "table_row_PY3VVn",
        "table_row_3ndXYW",
        "table_row_zq9fUB",
        "table_row_KzM7n7",
        "table_row_TrGQNE"
      ],
      "settings": {
        "content_align": "center",
        "content_align_mobile": "center",
        "heading": "<p>It's Not Even Close</p>",
        "heading_custom": false,
        "heading_font": "josefin_sans_n4",
        "heading_size": 56,
        "heading_size_mobile": 28,
        "heading_height": 130,
        "text": "",
        "text_custom": false,
        "text_font": "josefin_sans_n4",
        "text_size": 18,
        "text_size_mobile": 14,
        "text_height": 150,
        "text_mt": 16,
        "text_mt_mobile": 16,
        "table_columns": 2,
        "table_radius": 20,
        "table_mt": 24,
        "table_mt_mobile": 12,
        "column_width": 250,
        "column_width_mobile": 120,
        "row_heading_width": 260,
        "row_heading_width_mobile": 150,
        "first_heading": "OS",
        "first_heading_image": "shopify://shop_images/icon-logo-star.svg",
        "first_heading_url": "",
        "second_heading": "OTHERS",
        "second_heading_image": "shopify://shop_images/thumb-down-gesture-svgrepo-com_06a8ddf3-915f-4cb2-8ca6-5f8b8ee4231b.svg",
        "second_heading_url": "",
        "third_heading": "",
        "third_heading_url": "",
        "four_heading": "",
        "four_heading_url": "",
        "fives_heading": "",
        "fives_heading_url": "",
        "sixth_heading": "",
        "sixth_heading_url": "",
        "heading_image_size": 70,
        "heading_image_size_mobile": 80,
        "heading_image_radius": 0,
        "heading_image_border_thickness": 0,
        "column_heading_custom": false,
        "column_heading_font": "assistant_n4",
        "column_heading_size": 24,
        "column_heading_size_mobile": 14,
        "column_heading_height": 130,
        "column_heading_align": "center",
        "column_heading_align_mobile": "center",
        "column_heading_padding_horizontal": 16,
        "column_heading_padding_horizontal_mobile": 4,
        "column_heading_padding_vertical": 24,
        "column_heading_padding_vertical_mobile": 20,
        "row_padding_horizontal": 16,
        "row_padding_horizontal_mobile": 4,
        "row_padding_vertical": 24,
        "row_padding_vertical_mobile": 20,
        "row_border_thickness": 1,
        "row_heading_custom": false,
        "row_heading_font": "assistant_n4",
        "row_heading_size": 22,
        "row_heading_size_mobile": 18,
        "row_heading_height": 130,
        "value_custom": false,
        "value_font": "assistant_n4",
        "value_size": 24,
        "value_size_mobile": 14,
        "value_height": 130,
        "value_icon_size": 28,
        "value_icon_size_mobile": 24,
        "first_column_icon_size": 28,
        "first_column_icon_size_mobile": 24,
        "second_column_icon_size": 22,
        "second_column_icon_size_mobile": 20,
        "third_column_icon_size": 28,
        "third_column_icon_size_mobile": 24,
        "versus_icon_size": 60,
        "versus_icon_size_mobile": 30,
        "bottom_text": "",
        "bottom_text_custom": false,
        "bottom_text_font": "josefin_sans_n4",
        "bottom_text_size": 12,
        "bottom_text_size_mobile": 12,
        "bottom_text_height": 150,
        "bottom_text_mt": 24,
        "bottom_text_mt_mobile": 20,
        "bottom_text_align": "left",
        "bottom_text_align_mobile": "left",
        "heading_color": "#000000",
        "text_color": "#000000",
        "bottom_text_color": "#afabab",
        "column_heading_color": "#000000",
        "column_heading_active_color": "#2a2a2a",
        "value_color": "#000000",
        "value_active_color": "#2a2a2a",
        "table_column_bg_color": "#e7e8e5",
        "table_column_active_bg_color": "#47de47",
        "heading_image_border_color": "#000000",
        "row_heading_color": "#000000",
        "row_border_color": "#d9d9d9",
        "value_icon_color": "#000000",
        "value_icon_active_color": "#2a2a2a",
        "versus_value_icon_color": "#ffffff",
        "versus_icon_bg_color": "#1a1a1a",
        "versus_vs_bg_color": "#2a2a2a",
        "background_color": "#e7e8e5",
        "background_gradient": "",
        "border_color": "#000000",
        "margin_top": 40,
        "margin_bottom": 76,
        "margin_horizontal": 0,
        "margin_horizontal_mobile": 0,
        "padding_top": 36,
        "padding_bottom": 36,
        "padding_horizontal": 5,
        "padding_horizontal_mobile": 1.5,
        "full_width": false,
        "content_width": 1200,
        "border_thickness": 0,
        "section_radius": 0,
        "lazy": true
      }
    },
    "slider_whats_inside": {
      "type": "slider",
      "blocks": {
        "testimonial_cqq4fN": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/guy-running-again.png",
            "title": "<p>Magnesium Malate</p>",
            "text": "<p>ENERGY</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_Arze6L": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/zen.jpg",
            "title": "<p>Magnesium Glycinate</p>",
            "text": "<p>CALM</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_Q9R8Ce": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/brain.jpg",
            "title": "<p>Magnesium Taurate</p>",
            "text": "<p>CLARITY</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_B7iaKG": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/pink_salt.jpg",
            "title": "<p>Himalayan Salt</p>",
            "text": "<p>BALANCE</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_3n68dz": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/sweat.jpg",
            "title": "<p>Potassium Citrate</p>",
            "text": "<p>HYDRATION</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_jr7y9P": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/plant.jpg",
            "title": "<p>Stevia Extract</p>",
            "text": "<p>NATURE</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_TBLjiK": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/raspberry4.jpg",
            "title": "<p>Natural Flavors</p>",
            "text": "<p>ORGANIC</p>",
            "name": "",
            "link_url": ""
          }
        }
      },
      "block_order": [
        "testimonial_cqq4fN",
        "testimonial_Arze6L",
        "testimonial_Q9R8Ce",
        "testimonial_B7iaKG",
        "testimonial_3n68dz",
        "testimonial_jr7y9P",
        "testimonial_TBLjiK"
      ],
      "settings": {
        "title": "<p>What's Inside?</p>",
        "subtitle": "",
        "color_text": "#2a2a2a",
        "color_bg": "#e7e8e5",
        "container": "contain",
        "color_bg_block": "transparent",
        "color_text_block": "#2a2a2a",
        "color_border_block": "#2a2a2a",
        "border_width_block": 0,
        "border-media": false,
        "block_padding": true,
        "show_stars": false,
        "shape": "rounded",
        "text_align": "center",
        "media_size": "3/4",
        "s_title_font_desktop": 64,
        "b_title_font_desktop": 22,
        "b_text_font_desktop": 22,
        "b_name_font_desktop": 22,
        "s_title_font_mobile": 26,
        "b_title_font_mobile": 22,
        "b_text_font_mobile": 22,
        "b_name_font_mobile": 22,
        "block_count": 3,
        "navigation": "arrows4",
        "rotate": false,
        "speed-slider": 4,
        "padding-lr": 0,
        "padding-lr-m": 1,
        "slide-space": 0,
        "padding_top": 60,
        "padding_bottom": 40,
        "css_class": "slider-reduced-spacing",
        "custom_css": "@media screen and (max-width: 749px) { #LT--slider_whats_inside { padding-bottom: 0 !important; } }"
      }
    },
    "related-products": {
      "type": "related-products",
      "disabled": true,
      "settings": {
        "recommendations_count": 6,
        "products": [],
        "full_width": true,
        "stack_products": true,
        "show_progress_bar": false,
        "products_per_row_mobile": "1",
        "products_per_row_desktop": 3,
        "subheading": "",
        "title": "You may also like",
        "content": "",
        "background": "",
        "background_gradient": "",
        "text_color": "",
        "heading_color": "",
        "heading_gradient": "",
        "product_card_background": "",
        "product_card_text_color": ""
      }
    },
    "17372431293a76cefd": {
      "type": "apps",
      "blocks": {
        "judge_me_reviews_review_widget_x7X7Rj": {
          "type": "shopify://apps/judge-me-reviews/blocks/review_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        }
      },
      "block_order": [
        "judge_me_reviews_review_widget_x7X7Rj"
      ],
      "settings": {
        "full_width": true,
        "remove_vertical_spacing": true,
        "remove_horizontal_spacing": false,
        "background": "",
        "background_gradient": "",
        "text_color": ""
      }
    },
    "faq_wyw4an": {
      "type": "faq",
      "blocks": {
        "item_a9GK4W": {
          "type": "item",
          "settings": {
            "title": "WHY THREE FORMS OF MAGNESIUM?",
            "content": "<p>Each has distinct biochemical properties: </p><p><strong>Magnesium Taurate: </strong>for <em>focus</em> and neurological function</p><p><strong>Magnesium Glycinate</strong>: for sleep quality and <em>calming</em> effects on the nervous system</p><p><strong>Magnesium Malate: </strong>for ATP production and <em>energy</em> metabolism</p><p></p>"
          }
        },
        "item_93EVNt": {
          "type": "item",
          "settings": {
            "title": "HOW DO I VERIFY YOUR TEST RESULTS?",
            "content": "<p>By clicking <em>here.</em> </p>"
          }
        },
        "item_MNXpd4": {
          "type": "item",
          "settings": {
            "title": "WHAT MAKES YOUR ELECTROLYTES DIFFERENT?",
            "content": "<ol><li>We deliver a substantial <em>300mg of magnesium</em> per serving using three highly bioavailable forms. While others may sneak in low doses of magnesium, ours has <em>enough for you to feel.</em>  </li><li>We publish <em>third-party test results</em> for every batch, <em>verifying active ingredients</em> and <em>absense of heavy metals</em>.</li><li> We've <em>eliminated unnecessary additives</em> common in other products - 0 sugar, 0 artificial sweeteners, 0 fillers, 0 preservatives, 0 artificial colors.</li><li>Our electrolyte profile reflects <em>optimal mineral ratios</em> for cellular function rather than simply following industry norms.  </li><li>Our flavors are <em>downright addicting</em>. </li></ol>"
          }
        },
        "item_JQWQ3K": {
          "type": "item",
          "settings": {
            "title": "WHY IS THERE 10 CALORIES IF THERE IS NO SUGAR?",
            "content": "<p>Magnesium taurate contains taurate (an amino acid), and magnesium glycinate contains glycine (another amino acid). Both provide approximately 4 calories per gram, just like protein. </p><p>Additionally, citric acid is used as a flavoring agent. It contains 3 calories per gram and has a low glycemic index.<br/><br/>Together, these will have a negligible impact on blood sugar making OS Electrolyte Pods ketogenic, low-carb, and diabetic friendly. </p>"
          }
        },
        "item_GeeC4b": {
          "type": "item",
          "settings": {
            "title": "HOW MANY CAN I HAVE PER DAY?",
            "content": "<p>Most users function optimally with 1-2 sticks daily. Each dosage loads you up with 300mg of magnesium, so we recommend staying under 3 sticks per day. </p>"
          }
        },
        "item_6GfVhV": {
          "type": "item",
          "settings": {
            "title": "HOW LONG WILL IT TAKE TO GET MY ORDER?",
            "content": "<p>It depends on where you are. Orders processed here will take 2-3 business days to arrive in lower 48 states. Overseas deliveries can take anywhere from 7-10 days. Delivery details will be provided in your confirmation email.</p>"
          }
        }
      },
      "block_order": [
        "item_a9GK4W",
        "item_93EVNt",
        "item_MNXpd4",
        "item_JQWQ3K",
        "item_GeeC4b",
        "item_6GfVhV"
      ],
      "custom_css": [
        "/* fkDevS */.accordion-box {background: transparent;}",
        ".accordion {border-bottom: 2px solid; padding-top: 10px; padding-bottom: 10px;}",
        ".accordion .accordion__toggle .circle-chevron {display: none;}",
        ".accordion__toggle:first-child {font-size: 1.5rem; font-weight: 600;}",
        "@media (max-width: 500px) {.accordion__toggle:first-child {font-size: 1.2rem; }}",
        " /* fkDevE */"
      ],
      "settings": {
        "full_width": true,
        "subheading": "",
        "title": "FAQs",
        "content": "",
        "team_avatar_width": 160,
        "support_hours": "Our customer support is available Monday to Friday: 8am-8:30pm.",
        "answer_time": "Average answer time: 0.25hrs.",
        "button_text": "",
        "button_url": "",
        "text_position": "center",
        "background": "",
        "background_gradient": "",
        "text_color": "rgba(0,0,0,0)",
        "heading_color": "",
        "heading_gradient": "",
        "button_background": "",
        "button_text_color": "",
        "accordion_background": "",
        "accordion_text_color": ""
      }
    }
  },
  "order": [
    "main",
    "ss_comparison_table_12_gwHBPX",
    "slider_whats_inside",
    "related-products",
    "17372431293a76cefd",
    "faq_wyw4an"
  ]
}
